(()=>{var e={};e.id=492,e.ids=[492],e.modules={846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},2704:()=>{},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3873:e=>{"use strict";e.exports=require("path")},4154:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>s.a,__next_app__:()=>u,pages:()=>m,routeModule:()=>p,tree:()=>d});var n=r(5239),a=r(8088),i=r(8170),s=r.n(i),o=r(893),l={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);r.d(t,l);let d={children:["",{children:["/_not-found",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.t.bind(r,7398,23)),"next/dist/client/components/not-found-error"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,8014)),"C:\\Users\\<USER>\\Desktop\\Meta A\\metaanalizmusavirlik\\Meta Analiz Musavirlik\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,5284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,m=[],u={require:r,loadChunk:()=>Promise.resolve()},p=new n.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/_not-found/page",pathname:"/_not-found",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},4181:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,6444,23)),Promise.resolve().then(r.t.bind(r,6042,23)),Promise.resolve().then(r.t.bind(r,8170,23)),Promise.resolve().then(r.t.bind(r,9477,23)),Promise.resolve().then(r.t.bind(r,9345,23)),Promise.resolve().then(r.t.bind(r,2089,23)),Promise.resolve().then(r.t.bind(r,6577,23)),Promise.resolve().then(r.t.bind(r,1307,23))},6494:(e,t,r)=>{Promise.resolve().then(r.bind(r,8875))},7237:(e,t,r)=>{"use strict";r.d(t,{default:()=>i});var n=r(687),a=r(3210);function i(){let[e,t]=(0,a.useState)(!1);return(0,n.jsx)("div",{className:"fixed bottom-6 left-6 z-50",children:(0,n.jsxs)("button",{onClick:()=>{let e=`https://wa.me/+905423800050?text=${encodeURIComponent("Merhaba, bilgi alabilir miyim?")}`;window.open(e,"_blank")},onMouseEnter:()=>t(!0),onMouseLeave:()=>t(!1),className:"group relative bg-green-500 hover:bg-green-600 text-white p-4 rounded-full shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-110","aria-label":"WhatsApp ile iletişime ge\xe7",children:[(0,n.jsx)("svg",{className:"w-6 h-6",fill:"currentColor",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:(0,n.jsx)("path",{d:"M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.893 3.488"})}),(0,n.jsxs)("div",{className:`absolute left-full ml-3 top-1/2 transform -translate-y-1/2 bg-gray-900 text-white text-sm px-3 py-2 rounded-lg whitespace-nowrap transition-all duration-300 ${e?"opacity-100 visible":"opacity-0 invisible"}`,children:["WhatsApp ile iletişime ge\xe7",(0,n.jsx)("div",{className:"absolute right-full top-1/2 transform -translate-y-1/2 border-4 border-transparent border-r-gray-900"})]}),(0,n.jsx)("div",{className:"absolute inset-0 bg-green-500 rounded-full animate-ping opacity-75"})]})})}},7333:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,6346,23)),Promise.resolve().then(r.t.bind(r,7924,23)),Promise.resolve().then(r.t.bind(r,5656,23)),Promise.resolve().then(r.t.bind(r,99,23)),Promise.resolve().then(r.t.bind(r,8243,23)),Promise.resolve().then(r.t.bind(r,8827,23)),Promise.resolve().then(r.t.bind(r,2763,23)),Promise.resolve().then(r.t.bind(r,7173,23))},8014:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>d,metadata:()=>o,viewport:()=>l});var n=r(7413),a=r(1421),i=r.n(a);r(2704);var s=r(8875);let o={metadataBase:new URL("https://metaanalizmusavirlik.com"),title:"Meta Analiz M\xfcşavirlik - Profesyonel Danışmanlık Hizmetleri",description:"2011 yılından bu yana İnsan Kaynakları, Mali M\xfcşavirlik, SGK Danışmanlığı ve Kurumsal Danışmanlık hizmetleri sunmaktayız.",keywords:"m\xfcşavirlik, danışmanlık, insan kaynakları, mali m\xfcşavirlik, sgk, emeklilik, kurumsal danışmanlık",authors:[{name:"Meta Analiz M\xfcşavirlik"}],robots:"index, follow",openGraph:{title:"Meta Analiz M\xfcşavirlik - Profesyonel Danışmanlık Hizmetleri",description:"2011 yılından bu yana İnsan Kaynakları, Mali M\xfcşavirlik, SGK Danışmanlığı ve Kurumsal Danışmanlık hizmetleri sunmaktayız.",type:"website",locale:"tr_TR"}},l={width:"device-width",initialScale:1};function d({children:e}){return(0,n.jsx)("html",{lang:"tr",children:(0,n.jsxs)("body",{className:i().className,children:[e,(0,n.jsx)(s.default,{})]})})}},8875:(e,t,r)=>{"use strict";r.d(t,{default:()=>n});let n=(0,r(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Meta A\\\\metaanalizmusavirlik\\\\Meta Analiz Musavirlik\\\\app\\\\components\\\\WhatsAppButton.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Meta A\\metaanalizmusavirlik\\Meta Analiz Musavirlik\\app\\components\\WhatsAppButton.tsx","default")},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9265:(e,t,r)=>{Promise.resolve().then(r.bind(r,7237))},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),n=t.X(0,[447,357],()=>r(4154));module.exports=n})();