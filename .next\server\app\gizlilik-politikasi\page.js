(()=>{var e={};e.id=438,e.ids=[438],e.modules={380:(e,i,l)=>{"use strict";l.r(i),l.d(i,{GlobalError:()=>n.a,__next_app__:()=>c,pages:()=>o,routeModule:()=>x,tree:()=>m});var a=l(5239),r=l(8088),s=l(8170),n=l.n(s),t=l(893),d={};for(let e in t)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>t[e]);l.d(i,d);let m={children:["",{children:["gizlilik-politikasi",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(l.bind(l,6077)),"C:\\Users\\<USER>\\Desktop\\Meta A\\metaanalizmusavirlik\\Meta Analiz Musavirlik\\app\\gizlilik-politikasi\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(l.bind(l,8014)),"C:\\Users\\<USER>\\Desktop\\Meta A\\metaanalizmusavirlik\\Meta Analiz Musavirlik\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(l.t.bind(l,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(l.t.bind(l,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(l.t.bind(l,5284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,o=["C:\\Users\\<USER>\\Desktop\\Meta A\\metaanalizmusavirlik\\Meta Analiz Musavirlik\\app\\gizlilik-politikasi\\page.tsx"],c={require:l,loadChunk:()=>Promise.resolve()},x=new a.AppPageRouteModule({definition:{kind:r.RouteKind.APP_PAGE,page:"/gizlilik-politikasi/page",pathname:"/gizlilik-politikasi",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:m}})},846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},2704:()=>{},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3873:e=>{"use strict";e.exports=require("path")},4181:(e,i,l)=>{Promise.resolve().then(l.t.bind(l,6444,23)),Promise.resolve().then(l.t.bind(l,6042,23)),Promise.resolve().then(l.t.bind(l,8170,23)),Promise.resolve().then(l.t.bind(l,9477,23)),Promise.resolve().then(l.t.bind(l,9345,23)),Promise.resolve().then(l.t.bind(l,2089,23)),Promise.resolve().then(l.t.bind(l,6577,23)),Promise.resolve().then(l.t.bind(l,1307,23))},4536:(e,i,l)=>{let{createProxy:a}=l(9844);e.exports=a("C:\\Users\\<USER>\\Desktop\\Meta A\\metaanalizmusavirlik\\Meta Analiz Musavirlik\\node_modules\\next\\dist\\client\\app-dir\\link.js")},4783:(e,i,l)=>{Promise.resolve().then(l.t.bind(l,5814,23))},5463:(e,i,l)=>{Promise.resolve().then(l.t.bind(l,4536,23))},6077:(e,i,l)=>{"use strict";l.r(i),l.d(i,{default:()=>t,metadata:()=>n});var a=l(7413),r=l(4536),s=l.n(r);let n={title:"Gizlilik Politikası | Meta Analiz M\xfcşavirlik",description:"Meta Analiz M\xfcşavirlik gizlilik politikası ve kişisel verilerin korunması hakkında bilgiler."};function t(){return(0,a.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,a.jsx)("section",{className:"bg-slate-900 text-white py-20",children:(0,a.jsx)("div",{className:"container mx-auto px-6",children:(0,a.jsxs)("div",{className:"max-w-4xl",children:[(0,a.jsx)("h1",{className:"text-4xl md:text-5xl font-light mb-6",children:"Gizlilik Politikası"}),(0,a.jsx)("p",{className:"text-xl text-gray-300",children:"Kişisel verilerinizin korunması ve gizliliği bizim i\xe7in \xf6nceliklidir"})]})})}),(0,a.jsx)("section",{className:"py-16",children:(0,a.jsx)("div",{className:"container mx-auto px-6",children:(0,a.jsx)("div",{className:"max-w-4xl mx-auto",children:(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-lg p-8 md:p-12",children:[(0,a.jsx)("div",{className:"mb-8 p-4 bg-blue-50 rounded-lg",children:(0,a.jsxs)("p",{className:"text-sm text-blue-800",children:[(0,a.jsx)("strong",{children:"Son G\xfcncelleme:"})," 2 Temmuz 2025"]})}),(0,a.jsxs)("div",{className:"mb-12",children:[(0,a.jsx)("h2",{className:"text-2xl font-semibold mb-4 text-gray-900",children:"Giriş"}),(0,a.jsx)("p",{className:"text-gray-700 leading-relaxed mb-4",children:"Meta Analiz M\xfcşavirlik olarak, size emanet edilen kişisel verilerin gizliliğini ve g\xfcvenliğini korumaya kendimizi adadık. 6698 sayılı Kişisel Verilerin Korunması Kanunu (KVKK) başta olmak \xfczere y\xfcr\xfcrl\xfckteki t\xfcm veri koruma mevzuatına uygun hareket etmekteyiz."}),(0,a.jsx)("p",{className:"text-gray-700 leading-relaxed",children:"Bu Gizlilik Politikası, hangi kişisel verileri topladığımız, bunları nasıl kullandığımız, koruduğumuz ve haklarınız hakkında bilgi vermektedir."})]}),(0,a.jsxs)("div",{className:"mb-12",children:[(0,a.jsx)("h2",{className:"text-2xl font-semibold mb-4 text-gray-900",children:"İ\xe7indekiler"}),(0,a.jsx)("div",{className:"bg-gray-50 p-6 rounded-lg",children:(0,a.jsxs)("ul",{className:"space-y-2",children:[(0,a.jsx)("li",{children:(0,a.jsx)("a",{href:"#veri-sorumlusu",className:"text-blue-600 hover:text-blue-800",children:"1. Veri Sorumlusu"})}),(0,a.jsx)("li",{children:(0,a.jsx)("a",{href:"#toplanan-veriler",className:"text-blue-600 hover:text-blue-800",children:"2. Toplanan Kişisel Veriler"})}),(0,a.jsx)("li",{children:(0,a.jsx)("a",{href:"#toplama-yontemleri",className:"text-blue-600 hover:text-blue-800",children:"3. Veri Toplama Y\xf6ntemleri"})}),(0,a.jsx)("li",{children:(0,a.jsx)("a",{href:"#isleme-amaclari",className:"text-blue-600 hover:text-blue-800",children:"4. Veri İşleme Ama\xe7ları"})}),(0,a.jsx)("li",{children:(0,a.jsx)("a",{href:"#hukuki-sebepler",className:"text-blue-600 hover:text-blue-800",children:"5. Hukuki Sebepler"})}),(0,a.jsx)("li",{children:(0,a.jsx)("a",{href:"#veri-paylasimi",className:"text-blue-600 hover:text-blue-800",children:"6. Veri Paylaşımı"})}),(0,a.jsx)("li",{children:(0,a.jsx)("a",{href:"#veri-guvenligi",className:"text-blue-600 hover:text-blue-800",children:"7. Veri G\xfcvenliği"})}),(0,a.jsx)("li",{children:(0,a.jsx)("a",{href:"#haklariniz",className:"text-blue-600 hover:text-blue-800",children:"8. Haklarınız"})}),(0,a.jsx)("li",{children:(0,a.jsx)("a",{href:"#cerezler",className:"text-blue-600 hover:text-blue-800",children:"9. \xc7erezler"})}),(0,a.jsx)("li",{children:(0,a.jsx)("a",{href:"#iletisim",className:"text-blue-600 hover:text-blue-800",children:"10. İletişim"})})]})})]}),(0,a.jsxs)("div",{id:"veri-sorumlusu",className:"mb-12",children:[(0,a.jsx)("h2",{className:"text-2xl font-semibold mb-4 text-gray-900",children:"1. Veri Sorumlusu"}),(0,a.jsx)("p",{className:"text-gray-700 leading-relaxed mb-4",children:"Bu Gizlilik Politikası kapsamında veri sorumlusu Meta Analiz M\xfcşavirlik'tir."}),(0,a.jsx)("div",{className:"bg-gray-50 p-4 rounded-lg",children:(0,a.jsxs)("p",{className:"text-gray-700",children:[(0,a.jsx)("strong",{children:"Meta Analiz M\xfcşavirlik"}),(0,a.jsx)("br",{}),"Adres: [Şirket Adresi]",(0,a.jsx)("br",{}),"Telefon: [Telefon Numarası]",(0,a.jsx)("br",{}),"E-posta: <EMAIL>"]})})]}),(0,a.jsxs)("div",{id:"toplanan-veriler",className:"mb-12",children:[(0,a.jsx)("h2",{className:"text-2xl font-semibold mb-4 text-gray-900",children:"2. Toplanan Kişisel Veriler"}),(0,a.jsx)("p",{className:"text-gray-700 leading-relaxed mb-4",children:"Hizmetlerimizi sunabilmek i\xe7in aşağıdaki kişisel veri kategorilerini toplayabiliriz:"}),(0,a.jsxs)("ul",{className:"list-disc list-inside space-y-2 text-gray-700 ml-4",children:[(0,a.jsx)("li",{children:"Kimlik bilgileri (ad, soyad, T.C. kimlik numarası)"}),(0,a.jsx)("li",{children:"İletişim bilgileri (telefon, e-posta, adres)"}),(0,a.jsx)("li",{children:"Mesleki bilgiler (şirket, pozisyon, iş deneyimi)"}),(0,a.jsx)("li",{children:"Finansal bilgiler (gelir, gider, vergi bilgileri)"}),(0,a.jsx)("li",{children:"Hukuki işlem bilgileri (s\xf6zleşmeler, belgeler)"}),(0,a.jsx)("li",{children:"Teknik veriler (IP adresi, \xe7erez bilgileri)"})]})]}),(0,a.jsxs)("div",{id:"toplama-yontemleri",className:"mb-12",children:[(0,a.jsx)("h2",{className:"text-2xl font-semibold mb-4 text-gray-900",children:"3. Veri Toplama Y\xf6ntemleri"}),(0,a.jsx)("p",{className:"text-gray-700 leading-relaxed mb-4",children:"Kişisel verilerinizi aşağıdaki y\xf6ntemlerle topluyoruz:"}),(0,a.jsxs)("ul",{className:"list-disc list-inside space-y-2 text-gray-700 ml-4",children:[(0,a.jsx)("li",{children:"Web sitemiz \xfczerinden doldurduğunuz formlar"}),(0,a.jsx)("li",{children:"E-posta, telefon veya y\xfcz y\xfcze iletişim"}),(0,a.jsx)("li",{children:"Hizmet s\xf6zleşmeleri ve belgeler"}),(0,a.jsx)("li",{children:"Yasal y\xfck\xfcml\xfcl\xfckler gereği kamu kurumlarından"}),(0,a.jsx)("li",{children:"İş ortaklarımız ve tedarik\xe7ilerimizden"})]})]}),(0,a.jsxs)("div",{id:"isleme-amaclari",className:"mb-12",children:[(0,a.jsx)("h2",{className:"text-2xl font-semibold mb-4 text-gray-900",children:"4. Veri İşleme Ama\xe7ları"}),(0,a.jsx)("p",{className:"text-gray-700 leading-relaxed mb-4",children:"Kişisel verilerinizi aşağıdaki ama\xe7larla işliyoruz:"}),(0,a.jsxs)("ul",{className:"list-disc list-inside space-y-2 text-gray-700 ml-4",children:[(0,a.jsx)("li",{children:"Mali m\xfcşavirlik hizmetlerinin sunulması"}),(0,a.jsx)("li",{children:"SGK danışmanlığı ve uyuşmazlık \xe7\xf6z\xfcm\xfc"}),(0,a.jsx)("li",{children:"İnsan kaynakları danışmanlığı"}),(0,a.jsx)("li",{children:"İş sağlığı ve g\xfcvenliği hizmetleri"}),(0,a.jsx)("li",{children:"Emeklilik işlemleri ve danışmanlığı"}),(0,a.jsx)("li",{children:"Kurumsal danışmanlık hizmetleri"}),(0,a.jsx)("li",{children:"Yasal y\xfck\xfcml\xfcl\xfcklerin yerine getirilmesi"}),(0,a.jsx)("li",{children:"M\xfcşteri memnuniyetinin \xf6l\xe7\xfclmesi"}),(0,a.jsx)("li",{children:"İletişim ve bilgilendirme"})]})]}),(0,a.jsxs)("div",{id:"hukuki-sebepler",className:"mb-12",children:[(0,a.jsx)("h2",{className:"text-2xl font-semibold mb-4 text-gray-900",children:"5. Hukuki Sebepler"}),(0,a.jsx)("p",{className:"text-gray-700 leading-relaxed mb-4",children:"Kişisel verilerinizi KVKK'nın 5. maddesinde belirtilen aşağıdaki hukuki sebeplere dayanarak işliyoruz:"}),(0,a.jsxs)("ul",{className:"list-disc list-inside space-y-2 text-gray-700 ml-4",children:[(0,a.jsx)("li",{children:"A\xe7ık rızanızın bulunması"}),(0,a.jsx)("li",{children:"S\xf6zleşmenin kurulması veya ifası i\xe7in gerekli olması"}),(0,a.jsx)("li",{children:"Yasal y\xfck\xfcml\xfcl\xfcğ\xfcn yerine getirilmesi"}),(0,a.jsx)("li",{children:"Meşru menfaatlerimizin bulunması"}),(0,a.jsx)("li",{children:"Kamu yararının s\xf6z konusu olması"})]})]}),(0,a.jsxs)("div",{id:"veri-paylasimi",className:"mb-12",children:[(0,a.jsx)("h2",{className:"text-2xl font-semibold mb-4 text-gray-900",children:"6. Veri Paylaşımı"}),(0,a.jsx)("p",{className:"text-gray-700 leading-relaxed mb-4",children:"Kişisel verilerinizi aşağıdaki durumlarda ve taraflarla paylaşabiliriz:"}),(0,a.jsxs)("ul",{className:"list-disc list-inside space-y-2 text-gray-700 ml-4",children:[(0,a.jsx)("li",{children:"Yasal y\xfck\xfcml\xfcl\xfckler gereği kamu kurumları"}),(0,a.jsx)("li",{children:"Hizmet sunumu i\xe7in iş ortaklarımız"}),(0,a.jsx)("li",{children:"Teknik destek sağlayıcıları"}),(0,a.jsx)("li",{children:"Yasal danışmanlarımız"}),(0,a.jsx)("li",{children:"Denetim kuruluşları"})]}),(0,a.jsx)("p",{className:"text-gray-700 leading-relaxed mt-4",children:"Verilerinizi paylaştığımız t\xfcm taraflar, KVKK ve ilgili mevzuata uygun hareket etmekle y\xfck\xfcml\xfcd\xfcr."})]}),(0,a.jsxs)("div",{id:"veri-guvenligi",className:"mb-12",children:[(0,a.jsx)("h2",{className:"text-2xl font-semibold mb-4 text-gray-900",children:"7. Veri G\xfcvenliği"}),(0,a.jsx)("p",{className:"text-gray-700 leading-relaxed mb-4",children:"Kişisel verilerinizin g\xfcvenliğini sağlamak i\xe7in aşağıdaki \xf6nlemleri alıyoruz:"}),(0,a.jsxs)("ul",{className:"list-disc list-inside space-y-2 text-gray-700 ml-4",children:[(0,a.jsx)("li",{children:"Teknik g\xfcvenlik \xf6nlemleri (şifreleme, g\xfcvenlik duvarı)"}),(0,a.jsx)("li",{children:"İdari g\xfcvenlik \xf6nlemleri (erişim kontrol\xfc, yetkilendirme)"}),(0,a.jsx)("li",{children:"Fiziksel g\xfcvenlik \xf6nlemleri (g\xfcvenli saklama)"}),(0,a.jsx)("li",{children:"Personel eğitimleri ve gizlilik taahh\xfctleri"}),(0,a.jsx)("li",{children:"D\xfczenli g\xfcvenlik denetimleri"})]})]}),(0,a.jsxs)("div",{id:"haklariniz",className:"mb-12",children:[(0,a.jsx)("h2",{className:"text-2xl font-semibold mb-4 text-gray-900",children:"8. Haklarınız"}),(0,a.jsx)("p",{className:"text-gray-700 leading-relaxed mb-4",children:"KVKK'nın 11. maddesi gereğince aşağıdaki haklara sahipsiniz:"}),(0,a.jsxs)("ul",{className:"list-disc list-inside space-y-2 text-gray-700 ml-4",children:[(0,a.jsx)("li",{children:"Kişisel veri işlenip işlenmediğini \xf6ğrenme"}),(0,a.jsx)("li",{children:"İşlenen kişisel veriler hakkında bilgi talep etme"}),(0,a.jsx)("li",{children:"İşleme amacını ve amacına uygun kullanılıp kullanılmadığını \xf6ğrenme"}),(0,a.jsx)("li",{children:"Yurt i\xe7i/dışında aktarılan \xfc\xe7\xfcnc\xfc kişileri bilme"}),(0,a.jsx)("li",{children:"Eksik/yanlış işlenen verilerin d\xfczeltilmesini isteme"}),(0,a.jsx)("li",{children:"Verilerin silinmesi/yok edilmesini isteme"}),(0,a.jsx)("li",{children:"D\xfczeltme/silme işlemlerinin \xfc\xe7\xfcnc\xfc kişilere bildirilmesini isteme"}),(0,a.jsx)("li",{children:"Otomatik sistemlerle analiz sonucuna itiraz etme"}),(0,a.jsx)("li",{children:"Zararın giderilmesini talep etme"})]})]}),(0,a.jsxs)("div",{id:"cerezler",className:"mb-12",children:[(0,a.jsx)("h2",{className:"text-2xl font-semibold mb-4 text-gray-900",children:"9. \xc7erezler"}),(0,a.jsxs)("p",{className:"text-gray-700 leading-relaxed mb-4",children:["Web sitemizde kullanıcı deneyimini iyileştirmek i\xe7in \xe7erezler kullanıyoruz. \xc7erezler hakkında detaylı bilgi i\xe7in ",(0,a.jsx)(s(),{href:"/cerez-politikasi",className:"text-blue-600 hover:text-blue-800",children:"\xc7erez Politikamızı"})," inceleyebilirsiniz."]})]}),(0,a.jsxs)("div",{id:"iletisim",className:"mb-12",children:[(0,a.jsx)("h2",{className:"text-2xl font-semibold mb-4 text-gray-900",children:"10. İletişim"}),(0,a.jsx)("p",{className:"text-gray-700 leading-relaxed mb-4",children:"Kişisel verilerinizle ilgili taleplerinizi aşağıdaki kanallardan iletebilirsiniz:"}),(0,a.jsxs)("div",{className:"bg-gray-50 p-6 rounded-lg",children:[(0,a.jsxs)("p",{className:"text-gray-700 mb-2",children:[(0,a.jsx)("strong",{children:"E-posta:"})," <EMAIL>"]}),(0,a.jsxs)("p",{className:"text-gray-700 mb-2",children:[(0,a.jsx)("strong",{children:"Posta:"})," Meta Analiz M\xfcşavirlik, [Şirket Adresi]"]}),(0,a.jsxs)("p",{className:"text-gray-700",children:[(0,a.jsx)("strong",{children:"Başvuru Formu:"})," ",(0,a.jsx)(s(),{href:"/kvkk-basvuru",className:"text-blue-600 hover:text-blue-800",children:"Online Başvuru Formu"})]})]}),(0,a.jsx)("p",{className:"text-gray-700 leading-relaxed mt-4",children:"Başvurularınızı en ge\xe7 30 g\xfcn i\xe7inde \xfccretsiz olarak yanıtlıyoruz. İşlemin ayrıca bir maliyet gerektirmesi durumunda KVKK tarafından belirlenen \xfccret alınabilir."})]}),(0,a.jsx)("div",{className:"border-t pt-8",children:(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"Bu Gizlilik Politikası d\xfczenli olarak g\xf6zden ge\xe7irilir ve g\xfcncellenebilir. Değişiklikler web sitemizde yayınlandığı tarihte y\xfcr\xfcrl\xfcğe girer."})})]})})})})]})}},6494:(e,i,l)=>{Promise.resolve().then(l.bind(l,8875))},7237:(e,i,l)=>{"use strict";l.d(i,{default:()=>s});var a=l(687),r=l(3210);function s(){let[e,i]=(0,r.useState)(!1);return(0,a.jsx)("div",{className:"fixed bottom-6 left-6 z-50",children:(0,a.jsxs)("button",{onClick:()=>{let e=`https://wa.me/+905423800050?text=${encodeURIComponent("Merhaba, bilgi alabilir miyim?")}`;window.open(e,"_blank")},onMouseEnter:()=>i(!0),onMouseLeave:()=>i(!1),className:"group relative bg-green-500 hover:bg-green-600 text-white p-3 rounded-full shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-110","aria-label":"WhatsApp ile iletişime ge\xe7",children:[(0,a.jsx)("svg",{className:"w-8 h-8 text-white",fill:"currentColor",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:(0,a.jsx)("path",{d:"M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.893 3.488"})}),(0,a.jsxs)("div",{className:`absolute left-full ml-3 top-1/2 transform -translate-y-1/2 bg-gray-900 text-white text-sm px-3 py-2 rounded-lg whitespace-nowrap transition-all duration-300 ${e?"opacity-100 visible":"opacity-0 invisible"}`,children:["WhatsApp ile iletişime ge\xe7",(0,a.jsx)("div",{className:"absolute right-full top-1/2 transform -translate-y-1/2 border-4 border-transparent border-r-gray-900"})]}),(0,a.jsx)("div",{className:"absolute inset-0 bg-green-500 rounded-full animate-ping opacity-75"})]})})}},7333:(e,i,l)=>{Promise.resolve().then(l.t.bind(l,6346,23)),Promise.resolve().then(l.t.bind(l,7924,23)),Promise.resolve().then(l.t.bind(l,5656,23)),Promise.resolve().then(l.t.bind(l,99,23)),Promise.resolve().then(l.t.bind(l,8243,23)),Promise.resolve().then(l.t.bind(l,8827,23)),Promise.resolve().then(l.t.bind(l,2763,23)),Promise.resolve().then(l.t.bind(l,7173,23))},8014:(e,i,l)=>{"use strict";l.r(i),l.d(i,{default:()=>m,metadata:()=>t,viewport:()=>d});var a=l(7413),r=l(1421),s=l.n(r);l(2704);var n=l(8875);let t={metadataBase:new URL("https://metaanalizmusavirlik.com"),title:"Meta Analiz M\xfcşavirlik - Profesyonel Danışmanlık Hizmetleri",description:"2011 yılından bu yana İnsan Kaynakları, Mali M\xfcşavirlik, SGK Danışmanlığı ve Kurumsal Danışmanlık hizmetleri sunmaktayız.",keywords:"m\xfcşavirlik, danışmanlık, insan kaynakları, mali m\xfcşavirlik, sgk, emeklilik, kurumsal danışmanlık",authors:[{name:"Meta Analiz M\xfcşavirlik"}],robots:"index, follow",openGraph:{title:"Meta Analiz M\xfcşavirlik - Profesyonel Danışmanlık Hizmetleri",description:"2011 yılından bu yana İnsan Kaynakları, Mali M\xfcşavirlik, SGK Danışmanlığı ve Kurumsal Danışmanlık hizmetleri sunmaktayız.",type:"website",locale:"tr_TR"}},d={width:"device-width",initialScale:1};function m({children:e}){return(0,a.jsx)("html",{lang:"tr",children:(0,a.jsxs)("body",{className:s().className,children:[e,(0,a.jsx)(n.default,{})]})})}},8875:(e,i,l)=>{"use strict";l.d(i,{default:()=>a});let a=(0,l(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Meta A\\\\metaanalizmusavirlik\\\\Meta Analiz Musavirlik\\\\app\\\\components\\\\WhatsAppButton.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Meta A\\metaanalizmusavirlik\\Meta Analiz Musavirlik\\app\\components\\WhatsAppButton.tsx","default")},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9265:(e,i,l)=>{Promise.resolve().then(l.bind(l,7237))},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")}};var i=require("../../webpack-runtime.js");i.C(e);var l=e=>i(i.s=e),a=i.X(0,[447,357,814],()=>l(380));module.exports=a})();