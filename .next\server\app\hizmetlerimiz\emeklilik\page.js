(()=>{var e={};e.id=946,e.ids=[946],e.modules={220:(e,i,l)=>{"use strict";l.r(i),l.d(i,{GlobalError:()=>t.a,__next_app__:()=>c,pages:()=>d,routeModule:()=>x,tree:()=>o});var a=l(5239),s=l(8088),r=l(8170),t=l.n(r),n=l(893),m={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(m[e]=()=>n[e]);l.d(i,m);let o={children:["",{children:["hizmetlerimiz",{children:["emeklilik",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(l.bind(l,5153)),"C:\\Users\\<USER>\\Desktop\\Meta A\\metaanalizmusavirlik\\Meta Analiz Musavirlik\\app\\hizmetlerimiz\\emeklilik\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(l.bind(l,8014)),"C:\\Users\\<USER>\\Desktop\\Meta A\\metaanalizmusavirlik\\Meta Analiz Musavirlik\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(l.t.bind(l,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(l.t.bind(l,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(l.t.bind(l,5284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,d=["C:\\Users\\<USER>\\Desktop\\Meta A\\metaanalizmusavirlik\\Meta Analiz Musavirlik\\app\\hizmetlerimiz\\emeklilik\\page.tsx"],c={require:l,loadChunk:()=>Promise.resolve()},x=new a.AppPageRouteModule({definition:{kind:s.RouteKind.APP_PAGE,page:"/hizmetlerimiz/emeklilik/page",pathname:"/hizmetlerimiz/emeklilik",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3105:(e,i,l)=>{Promise.resolve().then(l.bind(l,5153))},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3873:e=>{"use strict";e.exports=require("path")},5153:(e,i,l)=>{"use strict";l.r(i),l.d(i,{default:()=>a});let a=(0,l(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Meta A\\\\metaanalizmusavirlik\\\\Meta Analiz Musavirlik\\\\app\\\\hizmetlerimiz\\\\emeklilik\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Meta A\\metaanalizmusavirlik\\Meta Analiz Musavirlik\\app\\hizmetlerimiz\\emeklilik\\page.tsx","default")},8943:(e,i,l)=>{"use strict";l.r(i),l.d(i,{default:()=>m});var a=l(687),s=l(5188),r=l(9386),t=l(5814),n=l.n(t);function m(){return(0,a.jsxs)("main",{className:"min-h-screen",children:[(0,a.jsx)(s.default,{currentPage:"hizmetlerimiz"}),(0,a.jsx)("section",{className:"pt-32 pb-16 bg-gradient-to-br from-slate-900 via-slate-800 to-slate-700",children:(0,a.jsx)("div",{className:"container mx-auto px-6",children:(0,a.jsxs)("div",{className:"text-center text-white",children:[(0,a.jsxs)("div",{className:"inline-flex items-center px-4 py-2 bg-white/10 rounded-full text-white text-sm font-medium mb-6 border border-white/20",children:[(0,a.jsx)("span",{className:"w-2 h-2 bg-blue-500 rounded-full mr-3"}),"Emeklilik Uzmanı"]}),(0,a.jsx)("h1",{className:"text-4xl md:text-6xl font-light mb-6 text-white",children:"Emeklilik Hizmetleri"}),(0,a.jsx)("p",{className:"text-xl text-white/90 max-w-3xl mx-auto",children:"Emeklilik hesaplamaları, bor\xe7lanma işlemleri ve emeklilik haklarınızla ilgili t\xfcm danışmanlık hizmetleri"})]})})}),(0,a.jsx)("section",{className:"py-24 bg-white",children:(0,a.jsx)("div",{className:"container mx-auto px-6",children:(0,a.jsx)("div",{className:"max-w-6xl mx-auto",children:(0,a.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-2 lg:grid-cols-3 gap-4 md:gap-8",children:[(0,a.jsxs)("div",{className:"bg-gradient-to-br from-blue-50 to-blue-100 p-4 md:p-8 rounded-2xl aspect-square flex flex-col",children:[(0,a.jsx)("div",{className:"w-12 h-12 md:w-16 md:h-16 bg-blue-600 rounded-xl flex items-center justify-center mb-4 md:mb-6 mx-auto",children:(0,a.jsx)("svg",{className:"w-8 h-8 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 7h6m0 10v-3m-3 3h.01M9 17h.01M9 14h.01M12 14h.01M15 11h.01M12 11h.01M9 11h.01M7 21h10a2 2 0 002-2V5a2 2 0 00-2-2H7a2 2 0 00-2 2v14a2 2 0 002 2z"})})}),(0,a.jsx)("h3",{className:"text-2xl font-semibold mb-4 text-gray-900",children:"Emeklilik Hesaplamaları"}),(0,a.jsx)("p",{className:"text-gray-700 leading-relaxed mb-4",children:"Mevcut durumunuz \xfczerinden detaylı emeklilik tahminleri ve hesaplamaları yapıyoruz"}),(0,a.jsxs)("ul",{className:"text-sm text-gray-600 space-y-2",children:[(0,a.jsx)("li",{children:"• Emeklilik yaşı hesaplama"}),(0,a.jsx)("li",{children:"• Emekli maaşı tahmini"}),(0,a.jsx)("li",{children:"• Prim g\xfcn sayısı kontrol\xfc"}),(0,a.jsx)("li",{children:"• Emeklilik şartları analizi"})]})]}),(0,a.jsxs)("div",{className:"bg-gradient-to-br from-green-50 to-green-100 p-8 rounded-2xl",children:[(0,a.jsx)("div",{className:"w-16 h-16 bg-green-600 rounded-xl flex items-center justify-center mb-6",children:(0,a.jsx)("svg",{className:"w-8 h-8 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"})})}),(0,a.jsx)("h3",{className:"text-2xl font-semibold mb-4 text-gray-900",children:"Bor\xe7lanma İşlemleri"}),(0,a.jsx)("p",{className:"text-gray-700 leading-relaxed mb-4",children:"Askerlik, \xf6ğrencilik ve diğer bor\xe7lanma t\xfcrleri i\xe7in hesaplama ve işlem takibi"}),(0,a.jsxs)("ul",{className:"text-sm text-gray-600 space-y-2",children:[(0,a.jsx)("li",{children:"• Askerlik bor\xe7lanması"}),(0,a.jsx)("li",{children:"• \xd6ğrencilik bor\xe7lanması"}),(0,a.jsx)("li",{children:"• Eksik hizmet bor\xe7lanması"}),(0,a.jsx)("li",{children:"• Bor\xe7lanma maliyeti hesaplama"})]})]}),(0,a.jsxs)("div",{className:"bg-gradient-to-br from-purple-50 to-purple-100 p-8 rounded-2xl",children:[(0,a.jsx)("div",{className:"w-16 h-16 bg-purple-600 rounded-xl flex items-center justify-center mb-6",children:(0,a.jsx)("svg",{className:"w-8 h-8 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"})})}),(0,a.jsx)("h3",{className:"text-2xl font-semibold mb-4 text-gray-900",children:"Hak Analizi"}),(0,a.jsx)("p",{className:"text-gray-700 leading-relaxed mb-4",children:"Emeklilik haklarınızın detaylı analizi ve optimizasyon \xf6nerileri"}),(0,a.jsxs)("ul",{className:"text-sm text-gray-600 space-y-2",children:[(0,a.jsx)("li",{children:"• Emeklilik hak analizi"}),(0,a.jsx)("li",{children:"• Ek \xf6deme hakları"}),(0,a.jsx)("li",{children:"• Yaşlılık aylığı hesaplama"}),(0,a.jsx)("li",{children:"• Malull\xfck analizi"})]})]})]})})})}),(0,a.jsx)("section",{className:"py-16 bg-gradient-to-br from-slate-900 via-slate-800 to-slate-700",children:(0,a.jsx)("div",{className:"container mx-auto px-6",children:(0,a.jsxs)("div",{className:"text-center text-white",children:[(0,a.jsx)("h3",{className:"text-3xl md:text-4xl font-light mb-6",children:"Emeklilik S\xfcrecinizde Yanınızdayız"}),(0,a.jsx)("p",{className:"text-xl text-white/90 mb-8 max-w-2xl mx-auto",children:"Uzman kadromuzla emeklilik haklarınızı en iyi şekilde değerlendirin"}),(0,a.jsxs)(n(),{href:"/iletisim",className:"inline-flex items-center px-8 py-4 bg-white text-slate-900 font-semibold rounded-lg hover:bg-gray-100 transition-colors text-lg",children:["İletişime Ge\xe7in",(0,a.jsx)("svg",{className:"w-5 h-5 ml-2",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M17 8l4 4m0 0l-4 4m4-4H3"})})]})]})})}),(0,a.jsx)(r.A,{})]})}},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9386:(e,i,l)=>{"use strict";l.d(i,{A:()=>n});var a=l(687),s=l(5814),r=l.n(s),t=l(474);function n(){return(0,a.jsx)("footer",{className:"bg-gray-900 text-white",children:(0,a.jsxs)("div",{className:"container mx-auto px-6 py-12",children:[(0,a.jsxs)("div",{className:"block md:hidden",children:[(0,a.jsx)("div",{className:"text-center mb-8",children:(0,a.jsx)("div",{className:"relative w-[180px] h-[40px] mx-auto",children:(0,a.jsx)(t.default,{src:"/logo6.webp",alt:"Meta Analiz M\xfcşavirlik Logo",fill:!0,className:"object-contain brightness-0 invert",sizes:"180px",priority:!0})})}),(0,a.jsxs)("div",{className:"text-center mb-8",children:[(0,a.jsx)("p",{className:"text-gray-300 mb-4 leading-relaxed text-sm",children:"2011 yılından bu yana m\xfcşterilerimize profesyonel danışmanlık hizmetleri sunmaktayız."}),(0,a.jsxs)("div",{className:"space-y-2 text-sm",children:[(0,a.jsxs)("p",{className:"text-gray-300",children:[(0,a.jsx)("span",{className:"font-semibold",children:"Telefon:"})," 0 (542) 797 05 00"]}),(0,a.jsxs)("p",{className:"text-gray-300",children:[(0,a.jsx)("span",{className:"font-semibold",children:"E-posta:"})," <EMAIL> - <EMAIL>"]})]})]})]}),(0,a.jsxs)("div",{className:"hidden md:grid md:grid-cols-2 lg:grid-cols-4 gap-8",children:[(0,a.jsxs)("div",{className:"lg:col-span-2",children:[(0,a.jsx)("div",{className:"flex items-center mb-6",children:(0,a.jsx)("div",{className:"relative w-[180px] h-[40px]",children:(0,a.jsx)(t.default,{src:"/logo6.webp",alt:"Meta Analiz M\xfcşavirlik Logo",fill:!0,className:"object-contain brightness-0 invert",sizes:"180px",priority:!0})})}),(0,a.jsx)("p",{className:"text-gray-300 mb-6 leading-relaxed",children:"2011 yılından bu yana m\xfcşterilerimize Y\xf6netim ve Yatırım M\xfcşavirliği, Finansal ve Mali Danışmanlık, İş ve Sosyal G\xfcvenlik M\xfcşavirliği, İnsan Kaynakları hizmetleri sunmaktayız."}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)("p",{className:"text-gray-300",children:[(0,a.jsx)("span",{className:"font-semibold",children:"Adres:"})," Yenicami Mah. \xd6zmen Sok. No: 24/A S\xf6ke Aydın"]}),(0,a.jsxs)("p",{className:"text-gray-300",children:[(0,a.jsx)("span",{className:"font-semibold",children:"Telefon:"})," 0 (542) 380 00 50"]}),(0,a.jsxs)("p",{className:"text-gray-300",children:[(0,a.jsx)("span",{className:"font-semibold",children:"E-posta:"})," <EMAIL> - <EMAIL>"]})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"text-lg font-semibold mb-6",children:"Hızlı Linkler"}),(0,a.jsxs)("ul",{className:"space-y-3",children:[(0,a.jsx)("li",{children:(0,a.jsx)(r(),{href:"/hakkimizda",className:"text-gray-300 hover:text-white transition-colors",children:"Hakkımızda"})}),(0,a.jsx)("li",{children:(0,a.jsx)(r(),{href:"/hizmetlerimiz",className:"text-gray-300 hover:text-white transition-colors",children:"Hizmetlerimiz"})}),(0,a.jsx)("li",{children:(0,a.jsx)(r(),{href:"/makalelerimiz",className:"text-gray-300 hover:text-white transition-colors",children:"Makalelerimiz"})}),(0,a.jsx)("li",{children:(0,a.jsx)(r(),{href:"/iletisim",className:"text-gray-300 hover:text-white transition-colors",children:"İletişim"})}),(0,a.jsx)("li",{children:(0,a.jsx)(r(),{href:"/toplanti-planla",className:"text-gray-300 hover:text-white transition-colors",children:"Toplantı Planla"})})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"text-lg font-semibold mb-6",children:"Hizmetlerimiz"}),(0,a.jsxs)("ul",{className:"space-y-3",children:[(0,a.jsx)("li",{children:(0,a.jsx)(r(),{href:"/hizmetlerimiz/yonetim-ve-yatirim-musavirligi",className:"text-gray-300 hover:text-white transition-colors",children:"Y\xf6netim ve Yatırım M\xfcşavirliği"})}),(0,a.jsx)("li",{children:(0,a.jsx)(r(),{href:"/hizmetlerimiz/mali-musavirlik",className:"text-gray-300 hover:text-white transition-colors",children:"Finansal ve Mali Danışmanlık/M\xfcşavirlik"})}),(0,a.jsx)("li",{children:(0,a.jsx)(r(),{href:"/hizmetlerimiz/sgk-uyusmazliklari",className:"text-gray-300 hover:text-white transition-colors",children:"İş ve Sosyal G\xfcvenlik M\xfcşavirliği"})}),(0,a.jsx)("li",{children:(0,a.jsx)(r(),{href:"/hizmetlerimiz/insan-kaynaklari",className:"text-gray-300 hover:text-white transition-colors",children:"İnsan Kaynakları"})}),(0,a.jsx)("li",{children:(0,a.jsx)(r(),{href:"/hizmetlerimiz/kurumsal-danismanlik",className:"text-gray-300 hover:text-white transition-colors",children:"Kurumsal Danışmanlık"})})]})]})]}),(0,a.jsx)("div",{className:"border-t border-gray-800 mt-12 pt-8",children:(0,a.jsxs)("div",{className:"flex flex-col md:flex-row justify-between items-center",children:[(0,a.jsx)("p",{className:"text-gray-400 text-xs md:text-sm text-center md:text-left",children:"\xa9 2025 Meta Analiz M\xfcşavirlik. T\xfcm hakları saklıdır."}),(0,a.jsxs)("div",{className:"flex space-x-6 mt-4 md:mt-0",children:[(0,a.jsx)(r(),{href:"/gizlilik-politikasi",className:"text-gray-400 hover:text-white text-sm transition-colors",children:"Gizlilik Politikası"}),(0,a.jsx)(r(),{href:"/kullanim-kosullari",className:"text-gray-400 hover:text-white text-sm transition-colors",children:"Kullanım Koşulları"})]})]})})]})})}},9553:(e,i,l)=>{Promise.resolve().then(l.bind(l,8943))}};var i=require("../../../webpack-runtime.js");i.C(e);var l=e=>i(i.s=e),a=i.X(0,[447,357,814,474,543],()=>l(220));module.exports=a})();