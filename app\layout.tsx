import type { Metadata } from 'next'
import { Inter } from 'next/font/google'
import './globals.css'
import WhatsAppButton from './components/WhatsAppButton'

const inter = Inter({ subsets: ['latin'] })

export const metadata: Metadata = {
  metadataBase: new URL('https://metaanalizmusavirlik.com'),
  title: 'Meta Analiz Müşavirlik - Profesyonel Danışmanlık Hizmetleri',
  description: '2011 yılından bu yana <PERSON>rı, Mali Müşavirlik, SGK Danışmanlığı ve Kurumsal Danışmanlık hizmetleri sunmaktayız.',
  keywords: 'm<PERSON><PERSON>avirlik, dan<PERSON><PERSON><PERSON><PERSON><PERSON>, insan kayna<PERSON>, mali <PERSON>, sgk, emek<PERSON>lik, kurumsal danışmanlık',
  authors: [{ name: 'Meta Analiz Müşavirlik' }],
  robots: 'index, follow',
  openGraph: {
    title: 'Meta Analiz <PERSON>üşavirlik - Profesyonel Danışmanlık Hizmetleri',
    description: '2011 yılından bu ya<PERSON>, Mali Müşavirlik, SGK Danışmanlığı ve Kurumsal Danışmanlık hizmetleri sunmaktayız.',
    type: 'website',
    locale: 'tr_TR',
  },
}

export const viewport = {
  width: 'device-width',
  initialScale: 1,
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="tr">
      <body className={inter.className}>
        {children}
        <WhatsAppButton />
      </body>
    </html>
  )
}
