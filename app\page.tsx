'use client';

import React from 'react';
import Header from './components/Header';
import Footer from './components/Footer';
import Link from 'next/link';
import Image from 'next/image';

export default function Home() {
  return (
    <main className="min-h-screen overflow-x-hidden">
      <style dangerouslySetInnerHTML={{
        __html: `
          .glass-btn {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 12px;
          }
          .glass-btn:hover {
            background: rgba(255, 255, 255, 0.2);
            border-color: rgba(255, 255, 255, 0.3);
          }
          .hero-text {
            max-width: 60rem;
            line-height: 1.6;
          }
          .hero-text br {
            display: block;
            content: "";
            margin-bottom: 0.5rem;
          }
          .mobile-break {
            display: none;
          }
          .desktop-break {
            display: block;
          }
          @media (max-width: 768px) {
            .hero-text {
              max-width: 100%;
              font-size: 1rem;
              line-height: 1.6; /* <PERSON><PERSON> sıkı satır aralığı */
            }
            .hero-text br {
              margin-bottom: 0; /* Boşluk kaldırıldı */
              line-height: 0;
            }
            .mobile-break {
              display: block;
              content: "";
              margin-bottom: 0; /* Boşluk kaldırıldı */
              line-height: 0;
            }
            .mobile-break::after {
              content: "\\A";
              white-space: pre;
            }
            .desktop-break {
              display: none;
            }
          }
        `
      }} />
      <Header currentPage="home" />
      
      {/* Hero Section */}
      <section className="relative min-h-screen flex items-center justify-center overflow-hidden pt-20 pb-8 md:pt-16">
        {/* Background Image */}
        <div className="absolute inset-0 z-0">
          <Image
            src="/hero.webp"
            alt="Profesyonel Danışmanlık/Müşavirlik Hizmetleri"
            fill
            className="object-cover"
            priority
            quality={85}
            sizes="100vw"
          />
        </div>

        {/* Gradient overlay for better text readability */}
        <div className="absolute inset-0 bg-gradient-to-r from-slate-900/70 via-slate-900/50 to-transparent z-10" />

        <div className="container mx-auto px-4 md:px-6 relative z-20 max-w-7xl overflow-hidden">
          <div className="flex justify-between items-center relative">
            {/* Main content */}
            <div className="flex-1 w-full max-w-4xl overflow-hidden">
              <div className="inline-flex items-center px-4 py-2 bg-white/10 rounded-full text-white text-sm font-medium mb-6 border border-white/20 backdrop-blur-sm">
                <span className="w-2 h-2 bg-blue-500 rounded-full mr-3"></span>
                2011'den Bu Yana Güvenilir Hizmet
              </div>
              <h1 className="text-2xl md:text-5xl lg:text-7xl font-semibold text-white mb-4 md:mb-6 leading-tight break-words overflow-hidden">
                Profesyonel<br />
                <span className="font-semibold text-white">
                  Danışmanlık/Müşavirlik<br className="md:hidden" /> Hizmetleri
                </span>
              </h1>
              <p className="text-base md:text-xl text-white/90 hero-text mb-6 md:mb-8 leading-relaxed break-words overflow-hidden max-w-2xl">
                Yönetim ve Yatırım Müşavirliği, <span className="mobile-break"></span>
                Finansal ve Mali Danışmanlık/Müşavirlik, <br className="desktop-break" />
                İş ve Sosyal Güvenlik Müşavirliği, <span className="mobile-break"></span>
                İnsan Kaynakları Hizmetleri, <br className="desktop-break" />
                Kurumsal Danışmanlık alanlarında <span className="mobile-break"></span>
                15 yılı aşkın deneyim ve uzman ekibimizle yanınızdayız.
              </p>
              <div className="flex flex-row gap-3 sm:gap-4">
                <Link
                  href="/hizmetlerimiz"
                  className="glass-btn inline-flex items-center justify-center px-3 sm:px-8 py-3 text-xs sm:text-lg font-medium text-white hover:scale-105 transition-all duration-300 flex-1 sm:flex-none whitespace-nowrap"
                >
                  <span className="text-xs sm:text-lg">
                    Hizmetlerimizi İnceleyin
                  </span>
                  <svg className="w-3 h-3 sm:w-5 sm:h-5 ml-1 sm:ml-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14 5l7 7m0 0l-7 7m7-7H3" />
                  </svg>
                </Link>
                <Link
                  href="/toplanti-planla"
                  className="group inline-flex items-center justify-center px-3 sm:px-8 py-3 bg-white/10 text-white font-semibold rounded-lg hover:bg-white/20 transition-all duration-300 text-xs sm:text-lg border border-white/20 backdrop-blur-sm hover:scale-105 transform flex-1 sm:flex-none whitespace-nowrap"
                >
                  <span className="text-xs sm:text-lg">Toplantı Planlayın</span>
                  <svg className="w-3 h-3 sm:w-5 sm:h-5 ml-1 sm:ml-2 group-hover:rotate-12 transition-transform duration-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                </Link>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Services Overview */}
      <section className="py-24 bg-gradient-to-br from-gray-50 to-gray-100">
        <div className="container mx-auto px-6">
          <div className="text-center mb-16">
            <div className="inline-flex items-center px-4 py-2 bg-white/80 rounded-full text-gray-800 text-sm font-medium mb-6 border border-gray-200">
              <span className="w-2 h-2 bg-blue-500 rounded-full mr-3"></span>
              Uzman Hizmetler
            </div>
            <h2 className="text-3xl md:text-4xl font-light mb-6 text-gray-900">
              Hizmet Alanlarımız
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Geniş hizmet yelpazemizle işletmenizin her alanında profesyonel destek sağlıyoruz
            </p>
          </div>

          <div className="grid grid-cols-2 md:grid-cols-2 lg:grid-cols-3 gap-4 md:gap-8">

            {/* İnsan Kaynakları */}
            <div className="group bg-white p-4 md:p-6 rounded-2xl border border-gray-200 hover:shadow-xl transition-all duration-300 flex flex-col h-auto">
              <div className="w-12 h-12 md:w-16 md:h-16 bg-slate-800 rounded-xl flex items-center justify-center mb-4 group-hover:bg-slate-700 transition-colors mx-auto">
                <svg className="w-6 h-6 md:w-8 md:h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                </svg>
              </div>
              <h3 className="text-lg md:text-xl font-semibold mb-3 text-gray-900 text-center">İnsan Kaynakları</h3>
              <p className="text-sm md:text-base text-gray-600 mb-4 leading-relaxed text-center">İşe alım, performans yönetimi, ücret sistemleri ve İK süreçlerinin optimizasyonu</p>
              <Link href="/hizmetlerimiz/insan-kaynaklari" className="text-slate-800 font-semibold hover:text-slate-600 transition-colors inline-flex items-center justify-center text-sm md:text-base mt-auto">
                Detayları Görün
                <svg className="w-3 h-3 md:w-4 md:h-4 ml-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 8l4 4m0 0l-4 4m4-4H3" />
                </svg>
              </Link>
            </div>

            {/* Mali Müşavirlik */}
            <div className="group bg-white p-4 md:p-6 rounded-2xl border border-gray-200 hover:shadow-xl transition-all duration-300 flex flex-col h-auto">
              <div className="w-12 h-12 md:w-16 md:h-16 bg-slate-800 rounded-xl flex items-center justify-center mb-4 group-hover:bg-slate-700 transition-colors mx-auto">
                <svg className="w-6 h-6 md:w-8 md:h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 7h6m0 10v-3m-3 3h.01M9 17h.01M9 14h.01M12 14h.01M15 11h.01M12 11h.01M9 11h.01M7 21h10a2 2 0 002-2V5a2 2 0 00-2-2H7a2 2 0 00-2 2v14a2 2 0 002 2z" />
                </svg>
              </div>
              <h3 className="text-sm md:text-xl font-semibold mb-3 text-gray-900 text-center leading-tight break-words">
                Mali Danışmanlık/<br className="sm:hidden" />Müşavirlik
              </h3>
              <p className="text-sm md:text-base text-gray-600 mb-4 leading-relaxed text-center">Muhasebe, vergi danışmanlığı, finansal planlama ve mali raporlama hizmetleri</p>
              <Link href="/hizmetlerimiz/mali-musavirlik" className="text-slate-800 font-semibold hover:text-slate-600 transition-colors inline-flex items-center justify-center text-sm md:text-base mt-auto">
                Detayları Görün
                <svg className="w-3 h-3 md:w-4 md:h-4 ml-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 8l4 4m0 0l-4 4m4-4H3" />
                </svg>
              </Link>
            </div>

            {/* Emeklilik Hizmetleri */}
            <div className="group bg-white p-4 md:p-6 rounded-2xl border border-gray-200 hover:shadow-xl transition-all duration-300 flex flex-col h-auto">
              <div className="w-12 h-12 md:w-16 md:h-16 bg-slate-800 rounded-xl flex items-center justify-center mb-4 group-hover:bg-slate-700 transition-colors mx-auto">
                <svg className="w-6 h-6 md:w-8 md:h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
                </svg>
              </div>
              <h3 className="text-lg md:text-xl font-semibold mb-3 text-gray-900 text-center">Emeklilik Hizmetleri</h3>
              <p className="text-sm md:text-base text-gray-600 mb-4 leading-relaxed text-center">Emeklilik hesaplamaları, borçlanma işlemleri ve emeklilik planlaması</p>
              <Link href="/hizmetlerimiz/emeklilik" className="text-slate-800 font-semibold hover:text-slate-600 transition-colors inline-flex items-center justify-center text-sm md:text-base mt-auto">
                Detayları Görün
                <svg className="w-3 h-3 md:w-4 md:h-4 ml-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 8l4 4m0 0l-4 4m4-4H3" />
                </svg>
              </Link>
            </div>

            {/* İş Sağlığı ve Güvenliği */}
            <div className="group bg-white p-4 md:p-6 rounded-2xl border border-gray-200 hover:shadow-xl transition-all duration-300 flex flex-col h-auto">
              <div className="w-12 h-12 md:w-16 md:h-16 bg-slate-800 rounded-xl flex items-center justify-center mb-4 group-hover:bg-slate-700 transition-colors mx-auto">
                <svg className="w-6 h-6 md:w-8 md:h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                </svg>
              </div>
              <h3 className="text-lg md:text-xl font-semibold mb-3 text-gray-900 text-center">İş Sağlığı ve Güvenliği</h3>
              <p className="text-sm md:text-base text-gray-600 mb-4 leading-relaxed text-center">İş güvenliği uzmanı hizmetleri, risk değerlendirme ve iş sağlığı danışmanlığı</p>
              <Link href="/hizmetlerimiz/is-sagligi-guvenligi" className="text-slate-800 font-semibold hover:text-slate-600 transition-colors inline-flex items-center justify-center text-sm md:text-base mt-auto">
                Detayları Görün
                <svg className="w-3 h-3 md:w-4 md:h-4 ml-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 8l4 4m0 0l-4 4m4-4H3" />
                </svg>
              </Link>
            </div>

            {/* Kurumsal Danışmanlık */}
            <div className="group bg-white p-4 md:p-6 rounded-2xl border border-gray-200 hover:shadow-xl transition-all duration-300 flex flex-col h-auto">
              <div className="w-12 h-12 md:w-16 md:h-16 bg-slate-800 rounded-xl flex items-center justify-center mb-4 group-hover:bg-slate-700 transition-colors mx-auto">
                <svg className="w-6 h-6 md:w-8 md:h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                </svg>
              </div>
              <h3 className="text-lg md:text-xl font-semibold mb-3 text-gray-900 text-center">Kurumsal Danışmanlık</h3>
              <p className="text-sm md:text-base text-gray-600 mb-4 leading-relaxed text-center">Stratejik planlama, organizasyon geliştirme ve kurumsal yönetim danışmanlığı</p>
              <Link href="/hizmetlerimiz/kurumsal-danismanlik" className="text-slate-800 font-semibold hover:text-slate-600 transition-colors inline-flex items-center justify-center text-sm md:text-base mt-auto">
                Detayları Görün
                <svg className="w-3 h-3 md:w-4 md:h-4 ml-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 8l4 4m0 0l-4 4m4-4H3" />
                </svg>
              </Link>
            </div>

            {/* SGK Uyuşmazlıkları */}
            <div className="group bg-white p-4 md:p-6 rounded-2xl border border-gray-200 hover:shadow-xl transition-all duration-300 flex flex-col h-auto">
              <div className="w-12 h-12 md:w-16 md:h-16 bg-slate-800 rounded-xl flex items-center justify-center mb-4 group-hover:bg-slate-700 transition-colors mx-auto">
                <svg className="w-6 h-6 md:w-8 md:h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 6l3 1m0 0l-3 9a5.002 5.002 0 006.001 0M6 7l3 9M6 7l6-2m6 2l3-1m-3 1l-3 9a5.002 5.002 0 006.001 0M18 7l3 9m-3-9l-6-2m0-2v2m0 16V5m0 16l3-1m-3 1l-3-1" />
                </svg>
              </div>
              <h3 className="text-lg md:text-xl font-semibold mb-3 text-gray-900 text-center">SGK Uyuşmazlıkları</h3>
              <p className="text-sm md:text-base text-gray-600 mb-4 leading-relaxed text-center">SGK ile yaşanan uyuşmazlıklarda hukuki destek ve danışmanlık hizmetleri</p>
              <Link href="/hizmetlerimiz/sgk-uyusmazliklari" className="text-slate-800 font-semibold hover:text-slate-600 transition-colors inline-flex items-center justify-center text-sm md:text-base mt-auto">
                Detayları Görün
                <svg className="w-3 h-3 md:w-4 md:h-4 ml-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 8l4 4m0 0l-4 4m4-4H3" />
                </svg>
              </Link>
            </div>

          </div>
        </div>
      </section>


      {/* Call to Action */}
      <section className="py-16 bg-gradient-to-br from-slate-900 via-slate-800 to-slate-700">
        <div className="container mx-auto px-6">
          <div className="text-center text-white">
            <div className="inline-flex items-center px-4 py-2 bg-white/10 rounded-full text-white text-sm font-medium mb-6 border border-white/20">
              <span className="w-2 h-2 bg-blue-500 rounded-full mr-3"></span>
              Hemen Başlayın
            </div>
            <h3 className="text-3xl md:text-4xl font-light mb-6">
              İşletmenizin Başarısı İçin Bugün Başlayın
            </h3>
            <p className="text-xl text-white/90 mb-8 max-w-2xl mx-auto">
              Uzman ekibimizle tanışın ve işletmenizin ihtiyaçlarına özel çözümler keşfedin.
            </p>
            <Link 
              href="/iletisim" 
              className="inline-flex items-center px-8 py-4 bg-white text-slate-900 font-semibold rounded-lg hover:bg-gray-100 transition-colors text-lg"
            >
              Hemen İletişime Geçin
              <svg className="w-5 h-5 ml-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 8l4 4m0 0l-4 4m4-4H3" />
              </svg>
            </Link>
          </div>
        </div>
      </section>

      {/* Success Stories & Testimonials */}
      <section className="py-24 bg-white">
        <div className="container mx-auto px-6">
          <div className="text-center mb-16">
            <div className="inline-flex items-center px-4 py-2 bg-gray-100 rounded-full text-gray-800 text-sm font-medium mb-6 border border-gray-200">
              <span className="w-2 h-2 bg-blue-500 rounded-full mr-3"></span>
              Başarı Hikayeleri
            </div>
            <h2 className="text-3xl md:text-4xl font-light mb-6 text-gray-900">
              Müşteri Başarı Hikayeleri
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              15 yıllık deneyimimizde birçok işletmeye değer kattık ve başarılarına ortak olduk
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-16">
            {/* Success Story 1 */}
            <div className="bg-gradient-to-br from-blue-50 to-blue-100 p-8 rounded-2xl">
              <div className="flex items-center mb-6">
                <div className="w-12 h-12 bg-blue-600 rounded-full flex items-center justify-center">
                  <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                  </svg>
                </div>
                <div className="ml-4">
                  <h4 className="text-lg font-semibold text-gray-900">%40 Verimlilik Artışı</h4>
                  <p className="text-sm text-gray-600">İnsan Kaynakları Optimizasyonu</p>
                </div>
              </div>
              <p className="text-gray-700 leading-relaxed">
                "Meta Analiz Müşavirlik ile çalışmaya başladıktan sonra İK süreçlerimizde %40 verimlilik artışı sağladık."
              </p>
              <div className="mt-4 text-sm text-gray-600">
                - Orta Ölçekli Üretim Firması
              </div>
            </div>

            {/* Success Story 2 */}
            <div className="bg-gradient-to-br from-green-50 to-green-100 p-8 rounded-2xl">
              <div className="flex items-center mb-6">
                <div className="w-12 h-12 bg-green-600 rounded-full flex items-center justify-center">
                  <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
                  </svg>
                </div>
                <div className="ml-4">
                  <h4 className="text-lg font-semibold text-gray-900">₺2.5M Vergi Tasarrufu</h4>
                  <p className="text-sm text-gray-600">Finansal ve Mali Danışmanlık/Müşavirlik Hizmetleri</p>
                </div>
              </div>
              <p className="text-gray-700 leading-relaxed">
                "Profesyonel Mali Danışmanlık/Müşavirlik hizmetleri sayesinde yıllık 2.5 milyon TL vergi tasarrufu sağladık."
              </p>
              <div className="mt-4 text-sm text-gray-600">
                - Teknoloji Şirketi
              </div>
            </div>

            {/* Success Story 3 */}
            <div className="bg-gradient-to-br from-purple-50 to-purple-100 p-8 rounded-2xl">
              <div className="flex items-center mb-6">
                <div className="w-12 h-12 bg-purple-600 rounded-full flex items-center justify-center">
                  <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                  </svg>
                </div>
                <div className="ml-4">
                  <h4 className="text-lg font-semibold text-gray-900">%100 Uyumluluk</h4>
                  <p className="text-sm text-gray-600">SGK ve İş Güvenliği</p>
                </div>
              </div>
              <p className="text-gray-700 leading-relaxed">
                "SGK uyuşmazlıklarımızı çözdük ve iş güvenliği konularında %100 uyumluluk sağladık."
              </p>
              <div className="mt-4 text-sm text-gray-600">
                - İnşaat Firması
              </div>
            </div>
          </div>


        </div>
      </section>

      <Footer />
    </main>
  );
}
