{"version": 3, "pages404": true, "caseSensitive": false, "basePath": "", "redirects": [{"source": "/:file((?!\\.well-known(?:/.*)?)(?:[^/]+/)*[^/]+\\.\\w+)/", "destination": "/:file", "internal": true, "missing": [{"type": "header", "key": "x-nextjs-data"}], "statusCode": 308, "regex": "^(?:/((?!\\.well-known(?:/.*)?)(?:[^/]+/)*[^/]+\\.\\w+))/$"}, {"source": "/:notfile((?!\\.well-known(?:/.*)?)(?:[^/]+/)*[^/\\.]+)", "destination": "/:notfile/", "internal": true, "statusCode": 308, "regex": "^(?:/((?!\\.well-known(?:/.*)?)(?:[^/]+/)*[^/\\.]+))$"}], "headers": [], "dynamicRoutes": [{"page": "/makalelerimiz/[slug]", "regex": "^/makalelerimiz/([^/]+?)(?:/)?$", "routeKeys": {"nxtPslug": "nxtPslug"}, "namedRegex": "^/makalelerimiz/(?<nxtPslug>[^/]+?)(?:/)?$"}], "staticRoutes": [{"page": "/", "regex": "^/(?:/)?$", "routeKeys": {}, "namedRegex": "^/(?:/)?$"}, {"page": "/_not-found", "regex": "^/_not\\-found(?:/)?$", "routeKeys": {}, "namedRegex": "^/_not\\-found(?:/)?$"}, {"page": "/farkliliklarimiz", "regex": "^/farkliliklarimiz(?:/)?$", "routeKeys": {}, "namedRegex": "^/farkliliklarimiz(?:/)?$"}, {"page": "/gizlilik-politikasi", "regex": "^/gizlilik\\-politikasi(?:/)?$", "routeKeys": {}, "namedRegex": "^/gizlilik\\-politikasi(?:/)?$"}, {"page": "/hakkimizda", "regex": "^/hakkimizda(?:/)?$", "routeKeys": {}, "namedRegex": "^/hakkimizda(?:/)?$"}, {"page": "/hedef-ve-il<PERSON><PERSON><PERSON>z", "regex": "^/hedef\\-ve\\-il<PERSON><PERSON><PERSON>z(?:/)?$", "routeKeys": {}, "namedRegex": "^/hedef\\-ve\\-il<PERSON><PERSON><PERSON>z(?:/)?$"}, {"page": "/hizmetlerimiz", "regex": "^/hizmetlerimiz(?:/)?$", "routeKeys": {}, "namedRegex": "^/hizmetlerimiz(?:/)?$"}, {"page": "/hizmetlerimiz/emeklilik", "regex": "^/hizmetlerimiz/emeklilik(?:/)?$", "routeKeys": {}, "namedRegex": "^/hizmetlerimiz/emeklilik(?:/)?$"}, {"page": "/hizmetlerimiz/insan-kaynaklari", "regex": "^/hizmetlerimiz/insan\\-kaynaklari(?:/)?$", "routeKeys": {}, "namedRegex": "^/hizmetlerimiz/insan\\-kaynaklari(?:/)?$"}, {"page": "/hizmetlerimiz/is-sagligi-guvenligi", "regex": "^/hizmetlerimiz/is\\-sagligi\\-guvenligi(?:/)?$", "routeKeys": {}, "namedRegex": "^/hizmetlerimiz/is\\-sagligi\\-guvenligi(?:/)?$"}, {"page": "/hizmetlerimiz/kurumsal-danis<PERSON>lik", "regex": "^/hizmetlerimiz/kurumsal\\-danismanlik(?:/)?$", "routeKeys": {}, "namedRegex": "^/hizmetlerimiz/kurumsal\\-danismanlik(?:/)?$"}, {"page": "/hizmetlerimiz/mali-musavirlik", "regex": "^/hizmetlerimiz/mali\\-musavirlik(?:/)?$", "routeKeys": {}, "namedRegex": "^/hizmetlerimiz/mali\\-musavirlik(?:/)?$"}, {"page": "/hizmetlerimiz/sgk-uyusmazliklari", "regex": "^/hizmetlerimiz/sgk\\-uyusmazliklari(?:/)?$", "routeKeys": {}, "namedRegex": "^/hizmetlerimiz/sgk\\-uyusmazliklari(?:/)?$"}, {"page": "/hizmetlerimiz/yonetim-ve-yati<PERSON>-musavirligi", "regex": "^/hizmetlerimiz/yonetim\\-ve\\-yatirim\\-musavirligi(?:/)?$", "routeKeys": {}, "namedRegex": "^/hizmetlerimiz/yonetim\\-ve\\-yatirim\\-musavirligi(?:/)?$"}, {"page": "/iletisim", "regex": "^/iletisim(?:/)?$", "routeKeys": {}, "namedRegex": "^/iletisim(?:/)?$"}, {"page": "/kullanim-kosullari", "regex": "^/kullanim\\-kosullari(?:/)?$", "routeKeys": {}, "namedRegex": "^/kullanim\\-kosullari(?:/)?$"}, {"page": "/makalele<PERSON>iz", "regex": "^/makalele<PERSON>iz(?:/)?$", "routeKeys": {}, "namedRegex": "^/makalele<PERSON>iz(?:/)?$"}, {"page": "/toplanti-planla", "regex": "^/toplanti\\-planla(?:/)?$", "routeKeys": {}, "namedRegex": "^/toplanti\\-planla(?:/)?$"}], "dataRoutes": [], "rsc": {"header": "RSC", "varyHeader": "RSC, Next-Router-State-Tree, Next-Router-Prefetch, Next-Router-Segment-Prefetch", "prefetchHeader": "Next-Router-Prefetch", "didPostponeHeader": "x-nextjs-postponed", "contentTypeHeader": "text/x-component", "suffix": ".rsc", "prefetchSuffix": ".prefetch.rsc", "prefetchSegmentHeader": "Next-Router-Segment-Prefetch", "prefetchSegmentSuffix": ".segment.rsc", "prefetchSegmentDirSuffix": ".segments"}, "rewriteHeaders": {"pathHeader": "x-nextjs-rewritten-path", "queryHeader": "x-nextjs-rewritten-query"}, "rewrites": []}