(()=>{var e={};e.id=157,e.ids=[157],e.modules={846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1227:(e,s,a)=>{Promise.resolve().then(a.bind(a,2872))},2872:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>n});var i=a(687),r=a(3210),t=a(5188),l=a(9386);function n(){let[e,s]=(0,r.useState)({name:"",email:"",phone:"",company:"",subject:"",message:""}),[a,n]=(0,r.useState)(!1),[o,d]=(0,r.useState)(""),[c,m]=(0,r.useState)(!1),x=a=>{s({...e,[a.target.name]:a.target.value})},h=async a=>{a.preventDefault(),n(!0),d(""),m(!1);try{let a=await fetch("/api/contact.php",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)}),i=await a.json();console.log("API Response:",i),a.ok&&i.success?(m(!0),d("Mesajınız başarıyla g\xf6nderildi! En kısa s\xfcrede size d\xf6n\xfcş yapacağız."),s({name:"",email:"",phone:"",company:"",subject:"",message:""}),setTimeout(()=>{d(""),m(!1)},5e3)):(m(!1),d("Hata: "+(i.message||i.error||"Bilinmeyen bir hata oluştu")))}catch(e){console.error("Contact form error:",e),m(!1),d("Bir hata oluştu. L\xfctfen tekrar deneyin.")}finally{n(!1)}};return(0,i.jsxs)("main",{className:"min-h-screen",children:[(0,i.jsx)(t.default,{currentPage:"iletisim"}),(0,i.jsx)("section",{className:"pt-32 pb-16 bg-gradient-to-br from-slate-900 via-slate-800 to-slate-700",children:(0,i.jsx)("div",{className:"container mx-auto px-6",children:(0,i.jsxs)("div",{className:"text-center text-white",children:[(0,i.jsxs)("div",{className:"inline-flex items-center px-4 py-2 bg-white/10 rounded-full text-white text-sm font-medium mb-6 border border-white/20",children:[(0,i.jsx)("span",{className:"w-2 h-2 bg-blue-500 rounded-full mr-3"}),"7/24 Destek"]}),(0,i.jsx)("h1",{className:"text-4xl md:text-6xl font-light mb-6 text-white",children:"İletişim"}),(0,i.jsx)("p",{className:"text-xl text-white/90 max-w-3xl mx-auto",children:"Sorularınız i\xe7in bizimle iletişime ge\xe7in, size en kısa s\xfcrede d\xf6n\xfcş yapalım"})]})})}),(0,i.jsx)("section",{className:"py-24 bg-white",children:(0,i.jsx)("div",{className:"container mx-auto px-6",children:(0,i.jsx)("div",{className:"max-w-6xl mx-auto",children:(0,i.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-12",children:[(0,i.jsxs)("div",{className:"order-1 lg:order-2",children:[(0,i.jsx)("h2",{className:"text-3xl font-light mb-8 text-gray-900",children:"Mesaj G\xf6nderin"}),o&&(0,i.jsx)("div",{className:`mb-6 p-4 rounded-lg transition-all duration-300 ${c?"bg-green-50 text-green-800 border border-green-200 shadow-sm":"bg-red-50 text-red-800 border border-red-200 shadow-sm"}`,children:(0,i.jsxs)("div",{className:"flex items-center",children:[c?(0,i.jsx)("svg",{className:"w-5 h-5 mr-3 text-green-600",fill:"currentColor",viewBox:"0 0 20 20",children:(0,i.jsx)("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z",clipRule:"evenodd"})}):(0,i.jsx)("svg",{className:"w-5 h-5 mr-3 text-red-600",fill:"currentColor",viewBox:"0 0 20 20",children:(0,i.jsx)("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z",clipRule:"evenodd"})}),(0,i.jsx)("span",{className:"font-medium",children:o})]})}),(0,i.jsxs)("form",{onSubmit:h,className:"space-y-6",children:[(0,i.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("label",{htmlFor:"name",className:"block text-sm font-medium text-gray-700 mb-2",children:"Ad Soyad *"}),(0,i.jsx)("input",{type:"text",id:"name",name:"name",value:e.name,onChange:x,required:!0,className:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors text-gray-900 bg-white",placeholder:"Adınız ve soyadınız"})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-700 mb-2",children:"E-posta *"}),(0,i.jsx)("input",{type:"email",id:"email",name:"email",value:e.email,onChange:x,required:!0,className:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors text-gray-900 bg-white",placeholder:"<EMAIL>"})]})]}),(0,i.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("label",{htmlFor:"phone",className:"block text-sm font-medium text-gray-700 mb-2",children:"Telefon *"}),(0,i.jsx)("input",{type:"tel",id:"phone",name:"phone",value:e.phone,onChange:x,required:!0,className:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors text-gray-900 bg-white",placeholder:"0 (5XX) XXX XX XX"})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("label",{htmlFor:"company",className:"block text-sm font-medium text-gray-700 mb-2",children:"Şirket"}),(0,i.jsx)("input",{type:"text",id:"company",name:"company",value:e.company,onChange:x,className:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors text-gray-900 bg-white",placeholder:"Şirket adınız"})]})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("label",{htmlFor:"subject",className:"block text-sm font-medium text-gray-700 mb-2",children:"Konu *"}),(0,i.jsxs)("select",{id:"subject",name:"subject",value:e.subject,onChange:x,required:!0,className:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors text-gray-900 bg-white",children:[(0,i.jsx)("option",{value:"",children:"Konu se\xe7iniz"}),(0,i.jsx)("option",{value:"mali-musavirlik",children:"Mali M\xfcşavirlik"}),(0,i.jsx)("option",{value:"insan-kaynaklari",children:"İnsan Kaynakları"}),(0,i.jsx)("option",{value:"sgk-danismanlik",children:"SGK Danışmanlığı"}),(0,i.jsx)("option",{value:"is-sagligi-guvenligi",children:"İş Sağlığı ve G\xfcvenliği"}),(0,i.jsx)("option",{value:"emeklilik",children:"Emeklilik Hizmetleri"}),(0,i.jsx)("option",{value:"kurumsal-danismanlik",children:"Kurumsal Danışmanlık"}),(0,i.jsx)("option",{value:"diger",children:"Diğer"})]})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("label",{htmlFor:"message",className:"block text-sm font-medium text-gray-700 mb-2",children:"Mesajınız *"}),(0,i.jsx)("textarea",{id:"message",name:"message",value:e.message,onChange:x,required:!0,rows:6,className:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors resize-none text-gray-900 bg-white",placeholder:"Mesajınızı buraya yazın..."})]}),(0,i.jsxs)("button",{type:"submit",disabled:a,className:`glass-btn w-full inline-flex items-center justify-center px-8 py-3 text-lg font-medium text-white transition-all duration-300 ${a?"opacity-50 cursor-not-allowed":"hover:scale-105"}`,children:[(0,i.jsx)("span",{children:a?"G\xf6nderiliyor...":"Mesaj G\xf6nder"}),!a&&(0,i.jsx)("svg",{className:"w-5 h-5 ml-2",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,i.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 19l9 2-9-18-9 18 9-2zm0 0v-8"})})]})]})]}),(0,i.jsxs)("div",{className:"order-2 lg:order-1",children:[(0,i.jsx)("h2",{className:"text-3xl font-light mb-8 text-gray-900",children:"İletişim Bilgileri"}),(0,i.jsxs)("div",{className:"space-y-6",children:[(0,i.jsxs)("div",{className:"flex items-start space-x-4",children:[(0,i.jsx)("div",{className:"w-12 h-12 bg-blue-600 rounded-lg flex items-center justify-center flex-shrink-0",children:(0,i.jsxs)("svg",{className:"w-6 h-6 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:[(0,i.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"}),(0,i.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M15 11a3 3 0 11-6 0 3 3 0 016 0z"})]})}),(0,i.jsxs)("div",{children:[(0,i.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:"Adres"}),(0,i.jsxs)("p",{className:"text-gray-600",children:["Yenicami Mah. \xd6zmen Sok. No: 24/A",(0,i.jsx)("br",{}),"S\xf6ke Aydın"]})]})]}),(0,i.jsxs)("div",{className:"flex items-start space-x-4",children:[(0,i.jsx)("div",{className:"w-12 h-12 bg-green-600 rounded-lg flex items-center justify-center flex-shrink-0",children:(0,i.jsx)("svg",{className:"w-6 h-6 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,i.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"})})}),(0,i.jsxs)("div",{children:[(0,i.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:"Telefon"}),(0,i.jsx)("p",{className:"text-gray-600",children:"0 (542) 797 05 00 - 0 (542) 380 00 50"})]})]}),(0,i.jsxs)("div",{className:"flex items-start space-x-4",children:[(0,i.jsx)("div",{className:"w-12 h-12 bg-purple-600 rounded-lg flex items-center justify-center flex-shrink-0",children:(0,i.jsx)("svg",{className:"w-6 h-6 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,i.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"})})}),(0,i.jsxs)("div",{children:[(0,i.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:"E-posta"}),(0,i.jsx)("p",{className:"text-gray-600",children:"<EMAIL>"}),(0,i.jsx)("p",{className:"text-gray-600",children:"<EMAIL>"})]})]}),(0,i.jsxs)("div",{className:"flex items-start space-x-4",children:[(0,i.jsx)("div",{className:"w-12 h-12 bg-orange-600 rounded-lg flex items-center justify-center flex-shrink-0",children:(0,i.jsx)("svg",{className:"w-6 h-6 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,i.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"})})}),(0,i.jsxs)("div",{children:[(0,i.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:"\xc7alışma Saatleri"}),(0,i.jsxs)("p",{className:"text-gray-600",children:["Pazartesi - Cuma: 09:00 - 18:00",(0,i.jsx)("br",{}),"Cumartesi: 09:00 - 13:00"]})]})]})]})]})]})})})}),(0,i.jsx)("section",{className:"py-0",children:(0,i.jsx)("div",{className:"w-full h-96",children:(0,i.jsx)("iframe",{src:"https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3141.234567890123!2d27.3123456!3d37.7543210!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x14bfd3e123456789%3A0x123456789abcdef!2sYenicami%20Mah.%20%C3%96zmen%20Sok.%20No%3A24%2FA%2C%2009270%20S%C3%B6ke%2FAyd%C4%B1n!5e0!3m2!1str!2str!4v1234567890123!5m2!1str!2str",width:"100%",height:"100%",style:{border:0},allowFullScreen:!0,loading:"lazy",referrerPolicy:"no-referrer-when-downgrade",title:"Meta Analiz M\xfcşavirlik Ofis Konumu - S\xf6ke, Aydın"})})}),(0,i.jsx)(l.A,{})]})}},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3170:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>i});let i=(0,a(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Meta A\\\\metaanalizmusavirlik\\\\Meta Analiz Musavirlik\\\\app\\\\iletisim\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Meta A\\metaanalizmusavirlik\\Meta Analiz Musavirlik\\app\\iletisim\\page.tsx","default")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3726:(e,s,a)=>{"use strict";a.r(s),a.d(s,{GlobalError:()=>l.a,__next_app__:()=>m,pages:()=>c,routeModule:()=>x,tree:()=>d});var i=a(5239),r=a(8088),t=a(8170),l=a.n(t),n=a(893),o={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>n[e]);a.d(s,o);let d={children:["",{children:["iletisim",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(a.bind(a,3170)),"C:\\Users\\<USER>\\Desktop\\Meta A\\metaanalizmusavirlik\\Meta Analiz Musavirlik\\app\\iletisim\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(a.bind(a,8014)),"C:\\Users\\<USER>\\Desktop\\Meta A\\metaanalizmusavirlik\\Meta Analiz Musavirlik\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(a.t.bind(a,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(a.t.bind(a,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(a.t.bind(a,5284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,c=["C:\\Users\\<USER>\\Desktop\\Meta A\\metaanalizmusavirlik\\Meta Analiz Musavirlik\\app\\iletisim\\page.tsx"],m={require:a,loadChunk:()=>Promise.resolve()},x=new i.AppPageRouteModule({definition:{kind:r.RouteKind.APP_PAGE,page:"/iletisim/page",pathname:"/iletisim",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},3873:e=>{"use strict";e.exports=require("path")},5571:(e,s,a)=>{Promise.resolve().then(a.bind(a,3170))},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9386:(e,s,a)=>{"use strict";a.d(s,{A:()=>n});var i=a(687),r=a(5814),t=a.n(r),l=a(474);function n(){return(0,i.jsx)("footer",{className:"bg-gray-900 text-white",children:(0,i.jsxs)("div",{className:"container mx-auto px-6 py-12",children:[(0,i.jsxs)("div",{className:"block md:hidden",children:[(0,i.jsx)("div",{className:"text-center mb-8",children:(0,i.jsx)("div",{className:"relative w-[180px] h-[40px] mx-auto",children:(0,i.jsx)(l.default,{src:"/logo6.webp",alt:"Meta Analiz M\xfcşavirlik Logo",fill:!0,className:"object-contain brightness-0 invert",sizes:"180px",priority:!0})})}),(0,i.jsxs)("div",{className:"text-center mb-8",children:[(0,i.jsx)("p",{className:"text-gray-300 mb-4 leading-relaxed text-sm",children:"2011 yılından bu yana m\xfcşterilerimize profesyonel danışmanlık hizmetleri sunmaktayız."}),(0,i.jsxs)("div",{className:"space-y-2 text-sm",children:[(0,i.jsxs)("p",{className:"text-gray-300",children:[(0,i.jsx)("span",{className:"font-semibold",children:"Telefon:"})," 0 (542) 797 05 00"]}),(0,i.jsxs)("p",{className:"text-gray-300",children:[(0,i.jsx)("span",{className:"font-semibold",children:"E-posta:"})," <EMAIL> - <EMAIL>"]})]})]})]}),(0,i.jsxs)("div",{className:"hidden md:grid md:grid-cols-2 lg:grid-cols-4 gap-8",children:[(0,i.jsxs)("div",{className:"lg:col-span-2",children:[(0,i.jsx)("div",{className:"flex items-center mb-6",children:(0,i.jsx)("div",{className:"relative w-[180px] h-[40px]",children:(0,i.jsx)(l.default,{src:"/logo6.webp",alt:"Meta Analiz M\xfcşavirlik Logo",fill:!0,className:"object-contain brightness-0 invert",sizes:"180px",priority:!0})})}),(0,i.jsx)("p",{className:"text-gray-300 mb-6 leading-relaxed",children:"2011 yılından bu yana m\xfcşterilerimize Y\xf6netim ve Yatırım M\xfcşavirliği, Finansal ve Mali Danışmanlık, İş ve Sosyal G\xfcvenlik M\xfcşavirliği, İnsan Kaynakları hizmetleri sunmaktayız."}),(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsxs)("p",{className:"text-gray-300",children:[(0,i.jsx)("span",{className:"font-semibold",children:"Adres:"})," Yenicami Mah. \xd6zmen Sok. No: 24/A S\xf6ke Aydın"]}),(0,i.jsxs)("p",{className:"text-gray-300",children:[(0,i.jsx)("span",{className:"font-semibold",children:"Telefon:"})," 0 (542) 380 00 50"]}),(0,i.jsxs)("p",{className:"text-gray-300",children:[(0,i.jsx)("span",{className:"font-semibold",children:"E-posta:"})," <EMAIL> - <EMAIL>"]})]})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("h4",{className:"text-lg font-semibold mb-6",children:"Hızlı Linkler"}),(0,i.jsxs)("ul",{className:"space-y-3",children:[(0,i.jsx)("li",{children:(0,i.jsx)(t(),{href:"/hakkimizda",className:"text-gray-300 hover:text-white transition-colors",children:"Hakkımızda"})}),(0,i.jsx)("li",{children:(0,i.jsx)(t(),{href:"/hizmetlerimiz",className:"text-gray-300 hover:text-white transition-colors",children:"Hizmetlerimiz"})}),(0,i.jsx)("li",{children:(0,i.jsx)(t(),{href:"/makalelerimiz",className:"text-gray-300 hover:text-white transition-colors",children:"Makalelerimiz"})}),(0,i.jsx)("li",{children:(0,i.jsx)(t(),{href:"/iletisim",className:"text-gray-300 hover:text-white transition-colors",children:"İletişim"})}),(0,i.jsx)("li",{children:(0,i.jsx)(t(),{href:"/toplanti-planla",className:"text-gray-300 hover:text-white transition-colors",children:"Toplantı Planla"})})]})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("h4",{className:"text-lg font-semibold mb-6",children:"Hizmetlerimiz"}),(0,i.jsxs)("ul",{className:"space-y-3",children:[(0,i.jsx)("li",{children:(0,i.jsx)(t(),{href:"/hizmetlerimiz/yonetim-ve-yatirim-musavirligi",className:"text-gray-300 hover:text-white transition-colors",children:"Y\xf6netim ve Yatırım M\xfcşavirliği"})}),(0,i.jsx)("li",{children:(0,i.jsx)(t(),{href:"/hizmetlerimiz/mali-musavirlik",className:"text-gray-300 hover:text-white transition-colors",children:"Finansal ve Mali Danışmanlık/M\xfcşavirlik"})}),(0,i.jsx)("li",{children:(0,i.jsx)(t(),{href:"/hizmetlerimiz/sgk-uyusmazliklari",className:"text-gray-300 hover:text-white transition-colors",children:"İş ve Sosyal G\xfcvenlik M\xfcşavirliği"})}),(0,i.jsx)("li",{children:(0,i.jsx)(t(),{href:"/hizmetlerimiz/insan-kaynaklari",className:"text-gray-300 hover:text-white transition-colors",children:"İnsan Kaynakları"})}),(0,i.jsx)("li",{children:(0,i.jsx)(t(),{href:"/hizmetlerimiz/kurumsal-danismanlik",className:"text-gray-300 hover:text-white transition-colors",children:"Kurumsal Danışmanlık"})})]})]})]}),(0,i.jsx)("div",{className:"border-t border-gray-800 mt-12 pt-8",children:(0,i.jsxs)("div",{className:"flex flex-col md:flex-row justify-between items-center",children:[(0,i.jsx)("p",{className:"text-gray-400 text-xs md:text-sm text-center md:text-left",children:"\xa9 2025 Meta Analiz M\xfcşavirlik. T\xfcm hakları saklıdır."}),(0,i.jsxs)("div",{className:"flex space-x-6 mt-4 md:mt-0",children:[(0,i.jsx)(t(),{href:"/gizlilik-politikasi",className:"text-gray-400 hover:text-white text-sm transition-colors",children:"Gizlilik Politikası"}),(0,i.jsx)(t(),{href:"/kullanim-kosullari",className:"text-gray-400 hover:text-white text-sm transition-colors",children:"Kullanım Koşulları"})]})]})})]})})}}};var s=require("../../webpack-runtime.js");s.C(e);var a=e=>s(s.s=e),i=s.X(0,[447,357,814,474,543],()=>a(3726));module.exports=i})();