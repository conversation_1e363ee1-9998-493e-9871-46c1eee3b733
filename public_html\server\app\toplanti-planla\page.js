(()=>{var e={};e.id=925,e.ids=[925],e.modules={249:(e,a,r)=>{Promise.resolve().then(r.bind(r,8298))},846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3873:e=>{"use strict";e.exports=require("path")},4884:(e,a,r)=>{"use strict";r.r(a),r.d(a,{default:()=>n});var s=r(687),t=r(3210),l=r(5188),i=r(9386);function n(){let[e,a]=(0,t.useState)({name:"",email:"",phone:"",company:"",service:"",preferred_date:"",preferred_time:"",meeting_type:"office",notes:""}),[r,n]=(0,t.useState)(!1),[o,m]=(0,t.useState)(""),[d,c]=(0,t.useState)(!1),x=r=>{a({...e,[r.target.name]:r.target.value})},p=async r=>{r.preventDefault(),n(!0),m(""),c(!1);try{let r=await fetch("/api/meeting.php",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)}),s=await r.json();console.log("Meeting API Response:",s),r.ok&&s.success?(c(!0),m("Toplantı talebiniz başarıyla g\xf6nderildi! En kısa s\xfcrede size d\xf6n\xfcş yapacağız."),a({name:"",email:"",phone:"",company:"",service:"",preferred_date:"",preferred_time:"",meeting_type:"office",notes:""}),setTimeout(()=>{m(""),c(!1)},5e3)):(c(!1),m("Hata: "+(s.message??s.error??"Bilinmeyen bir hata oluştu")))}catch(e){console.error("Meeting form error:",e),c(!1),m("Bir hata oluştu. L\xfctfen tekrar deneyin.")}finally{n(!1)}},h=new Date().toISOString().split("T")[0];return(0,s.jsxs)("main",{className:"min-h-screen",children:[(0,s.jsx)(l.default,{currentPage:"toplanti-planla"}),(0,s.jsx)("section",{className:"pt-32 pb-16 bg-gradient-to-br from-slate-900 via-slate-800 to-slate-700",children:(0,s.jsx)("div",{className:"container mx-auto px-6",children:(0,s.jsxs)("div",{className:"text-center text-white",children:[(0,s.jsxs)("div",{className:"inline-flex items-center px-4 py-2 bg-white/10 rounded-full text-white text-sm font-medium mb-6 border border-white/20",children:[(0,s.jsx)("span",{className:"w-2 h-2 bg-blue-500 rounded-full mr-3"}),"\xdccretsiz Danışmanlık/M\xfcşavirlik"]}),(0,s.jsx)("h1",{className:"text-4xl md:text-6xl font-light mb-6 text-white",children:"Toplantı Planla"}),(0,s.jsx)("p",{className:"text-xl text-white/90 max-w-3xl mx-auto",children:"Konusunda uzman danışman/m\xfcşavir ekibimizle g\xf6r\xfcşme planlamak i\xe7in formu doldurun"})]})})}),(0,s.jsx)("section",{className:"py-24 bg-white",children:(0,s.jsx)("div",{className:"container mx-auto px-6",children:(0,s.jsxs)("div",{className:"max-w-4xl mx-auto",children:[o&&(0,s.jsx)("div",{className:`mb-8 p-4 rounded-lg transition-all duration-300 ${d?"bg-green-50 text-green-800 border border-green-200 shadow-sm":"bg-red-50 text-red-800 border border-red-200 shadow-sm"}`,children:(0,s.jsxs)("div",{className:"flex items-center",children:[d?(0,s.jsx)("svg",{className:"w-5 h-5 mr-3 text-green-600",fill:"currentColor",viewBox:"0 0 20 20",children:(0,s.jsx)("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z",clipRule:"evenodd"})}):(0,s.jsx)("svg",{className:"w-5 h-5 mr-3 text-red-600",fill:"currentColor",viewBox:"0 0 20 20",children:(0,s.jsx)("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z",clipRule:"evenodd"})}),(0,s.jsx)("span",{className:"font-medium",children:o})]})}),(0,s.jsxs)("form",{onSubmit:p,className:"space-y-4 sm:space-y-6",children:[(0,s.jsxs)("div",{className:"grid grid-cols-1 sm:grid-cols-2 gap-4 sm:gap-6",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"name",className:"block text-sm font-medium text-gray-700 mb-1 sm:mb-2",children:"Ad Soyad *"}),(0,s.jsx)("input",{type:"text",id:"name",name:"name",value:e.name,onChange:x,required:!0,className:"w-full px-3 py-2 sm:px-4 sm:py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-gray-900 bg-white text-sm sm:text-base",placeholder:"Adınız ve soyadınız"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-700 mb-1 sm:mb-2",children:"E-posta *"}),(0,s.jsx)("input",{type:"email",id:"email",name:"email",value:e.email,onChange:x,required:!0,className:"w-full px-3 py-2 sm:px-4 sm:py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-gray-900 bg-white text-sm sm:text-base",placeholder:"<EMAIL>"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"phone",className:"block text-sm font-medium text-gray-700 mb-1 sm:mb-2",children:"Telefon *"}),(0,s.jsx)("input",{type:"tel",id:"phone",name:"phone",value:e.phone,onChange:x,required:!0,className:"w-full px-3 py-2 sm:px-4 sm:py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-gray-900 bg-white text-sm sm:text-base",placeholder:"05XX XXX XX XX"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"company",className:"block text-sm font-medium text-gray-700 mb-1 sm:mb-2",children:"Şirket"}),(0,s.jsx)("input",{type:"text",id:"company",name:"company",value:e.company,onChange:x,className:"w-full px-3 py-2 sm:px-4 sm:py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-gray-900 bg-white text-sm sm:text-base",placeholder:"Şirket adınız"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"service",className:"block text-sm font-medium text-gray-700 mb-1 sm:mb-2",children:"Hizmet T\xfcr\xfc *"}),(0,s.jsxs)("select",{id:"service",name:"service",value:e.service,onChange:x,required:!0,className:"w-full px-3 py-2 sm:px-4 sm:py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-gray-900 text-sm sm:text-base",children:[(0,s.jsx)("option",{value:"",className:"text-gray-500",children:"Hizmet se\xe7in"}),[{value:"insan-kaynaklari",label:"İnsan Kaynakları Hizmetleri"},{value:"emeklilik",label:"Emeklilik Hizmetleri"},{value:"kurumsal-danismanlik",label:"Kurumsal Danışmanlık"},{value:"sgk-uyusmazliklari",label:"SGK Uyuşmazlıkları"},{value:"is-sagligi-guvenligi",label:"İş Sağlığı ve G\xfcvenliği"},{value:"mali-musavirlik",label:"Mali M\xfcşavirlik"},{value:"genel-danismanlik",label:"Genel Danışmanlık"}].map(e=>(0,s.jsx)("option",{value:e.value,className:"text-gray-900",children:e.label},e.value))]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"meeting_type",className:"block text-sm font-medium text-gray-700 mb-1 sm:mb-2",children:"Toplantı T\xfcr\xfc *"}),(0,s.jsx)("select",{id:"meeting_type",name:"meeting_type",value:e.meeting_type,onChange:x,required:!0,className:"w-full px-3 py-2 sm:px-4 sm:py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-gray-900 text-sm sm:text-base",children:[{value:"office",label:"Ofiste G\xf6r\xfcşme"},{value:"online",label:"Online G\xf6r\xfcşme"},{value:"phone",label:"Telefon G\xf6r\xfcşmesi"}].map(e=>(0,s.jsx)("option",{value:e.value,className:"text-gray-900",children:e.label},e.value))})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"preferred_date",className:"block text-sm font-medium text-gray-700 mb-1 sm:mb-2",children:"Tercih Edilen Tarih *"}),(0,s.jsx)("input",{type:"date",id:"preferred_date",name:"preferred_date",value:e.preferred_date,onChange:x,min:h,required:!0,className:"w-full px-3 py-2 sm:px-4 sm:py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-gray-900 bg-white text-sm sm:text-base"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"preferred_time",className:"block text-sm font-medium text-gray-700 mb-1 sm:mb-2",children:"Tercih Edilen Saat *"}),(0,s.jsx)("input",{type:"time",id:"preferred_time",name:"preferred_time",value:e.preferred_time,onChange:x,min:"09:00",max:"18:00",required:!0,className:"w-full px-3 py-2 sm:px-4 sm:py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-gray-900 bg-white text-sm sm:text-base"}),(0,s.jsx)("p",{className:"text-sm text-gray-500 mt-1",children:"\xc7alışma saatleri: 09:00 - 18:00"})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"notes",className:"block text-sm font-medium text-gray-700 mb-2",children:"Ek Notlar"}),(0,s.jsx)("textarea",{id:"notes",name:"notes",value:e.notes,onChange:x,rows:4,className:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-gray-900 bg-white",placeholder:"Toplantı ile ilgili \xf6zel notlarınız varsa buraya yazabilirsiniz..."})]}),(0,s.jsx)("div",{className:"text-center",children:(0,s.jsxs)("button",{type:"submit",disabled:r,className:`glass-btn inline-flex items-center px-8 py-3 text-lg font-medium text-white transition-all duration-300 ${r?"opacity-50 cursor-not-allowed":"hover:scale-105"}`,children:[(0,s.jsx)("span",{children:r?"G\xf6nderiliyor...":"Toplantı Talep Et"}),!r&&(0,s.jsx)("svg",{className:"w-5 h-5 ml-2",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"})})]})})]})]})})}),(0,s.jsx)(i.A,{})]})}},6697:(e,a,r)=>{Promise.resolve().then(r.bind(r,4884))},8298:(e,a,r)=>{"use strict";r.r(a),r.d(a,{default:()=>s});let s=(0,r(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Meta A\\\\metaanalizmusavirlik\\\\Meta Analiz Musavirlik\\\\app\\\\toplanti-planla\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Meta A\\metaanalizmusavirlik\\Meta Analiz Musavirlik\\app\\toplanti-planla\\page.tsx","default")},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9386:(e,a,r)=>{"use strict";r.d(a,{A:()=>n});var s=r(687),t=r(5814),l=r.n(t),i=r(474);function n(){return(0,s.jsx)("footer",{className:"bg-gray-900 text-white",children:(0,s.jsxs)("div",{className:"container mx-auto px-6 py-12",children:[(0,s.jsxs)("div",{className:"block md:hidden",children:[(0,s.jsx)("div",{className:"text-center mb-8",children:(0,s.jsx)("div",{className:"relative w-[180px] h-[40px] mx-auto",children:(0,s.jsx)(i.default,{src:"/logo6.webp",alt:"Meta Analiz M\xfcşavirlik Logo",fill:!0,className:"object-contain brightness-0 invert",sizes:"180px",priority:!0})})}),(0,s.jsxs)("div",{className:"text-center mb-8",children:[(0,s.jsx)("p",{className:"text-gray-300 mb-4 leading-relaxed text-sm",children:"2011 yılından bu yana m\xfcşterilerimize profesyonel danışmanlık hizmetleri sunmaktayız."}),(0,s.jsxs)("div",{className:"space-y-2 text-sm",children:[(0,s.jsxs)("p",{className:"text-gray-300",children:[(0,s.jsx)("span",{className:"font-semibold",children:"Telefon:"})," 0 (542) 797 05 00"]}),(0,s.jsxs)("p",{className:"text-gray-300",children:[(0,s.jsx)("span",{className:"font-semibold",children:"E-posta:"})," <EMAIL> - <EMAIL>"]})]})]})]}),(0,s.jsxs)("div",{className:"hidden md:grid md:grid-cols-2 lg:grid-cols-4 gap-8",children:[(0,s.jsxs)("div",{className:"lg:col-span-2",children:[(0,s.jsx)("div",{className:"flex items-center mb-6",children:(0,s.jsx)("div",{className:"relative w-[180px] h-[40px]",children:(0,s.jsx)(i.default,{src:"/logo6.webp",alt:"Meta Analiz M\xfcşavirlik Logo",fill:!0,className:"object-contain brightness-0 invert",sizes:"180px",priority:!0})})}),(0,s.jsx)("p",{className:"text-gray-300 mb-6 leading-relaxed",children:"2011 yılından bu yana m\xfcşterilerimize Y\xf6netim ve Yatırım M\xfcşavirliği, Finansal ve Mali Danışmanlık, İş ve Sosyal G\xfcvenlik M\xfcşavirliği, İnsan Kaynakları hizmetleri sunmaktayız."}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsxs)("p",{className:"text-gray-300",children:[(0,s.jsx)("span",{className:"font-semibold",children:"Adres:"})," Yenicami Mah. \xd6zmen Sok. No: 24/A S\xf6ke Aydın"]}),(0,s.jsxs)("p",{className:"text-gray-300",children:[(0,s.jsx)("span",{className:"font-semibold",children:"Telefon:"})," 0 (542) 380 00 50"]}),(0,s.jsxs)("p",{className:"text-gray-300",children:[(0,s.jsx)("span",{className:"font-semibold",children:"E-posta:"})," <EMAIL> - <EMAIL>"]})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h4",{className:"text-lg font-semibold mb-6",children:"Hızlı Linkler"}),(0,s.jsxs)("ul",{className:"space-y-3",children:[(0,s.jsx)("li",{children:(0,s.jsx)(l(),{href:"/hakkimizda",className:"text-gray-300 hover:text-white transition-colors",children:"Hakkımızda"})}),(0,s.jsx)("li",{children:(0,s.jsx)(l(),{href:"/hizmetlerimiz",className:"text-gray-300 hover:text-white transition-colors",children:"Hizmetlerimiz"})}),(0,s.jsx)("li",{children:(0,s.jsx)(l(),{href:"/makalelerimiz",className:"text-gray-300 hover:text-white transition-colors",children:"Makalelerimiz"})}),(0,s.jsx)("li",{children:(0,s.jsx)(l(),{href:"/iletisim",className:"text-gray-300 hover:text-white transition-colors",children:"İletişim"})}),(0,s.jsx)("li",{children:(0,s.jsx)(l(),{href:"/toplanti-planla",className:"text-gray-300 hover:text-white transition-colors",children:"Toplantı Planla"})})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h4",{className:"text-lg font-semibold mb-6",children:"Hizmetlerimiz"}),(0,s.jsxs)("ul",{className:"space-y-3",children:[(0,s.jsx)("li",{children:(0,s.jsx)(l(),{href:"/hizmetlerimiz/yonetim-ve-yatirim-musavirligi",className:"text-gray-300 hover:text-white transition-colors",children:"Y\xf6netim ve Yatırım M\xfcşavirliği"})}),(0,s.jsx)("li",{children:(0,s.jsx)(l(),{href:"/hizmetlerimiz/mali-musavirlik",className:"text-gray-300 hover:text-white transition-colors",children:"Finansal ve Mali Danışmanlık/M\xfcşavirlik"})}),(0,s.jsx)("li",{children:(0,s.jsx)(l(),{href:"/hizmetlerimiz/sgk-uyusmazliklari",className:"text-gray-300 hover:text-white transition-colors",children:"İş ve Sosyal G\xfcvenlik M\xfcşavirliği"})}),(0,s.jsx)("li",{children:(0,s.jsx)(l(),{href:"/hizmetlerimiz/insan-kaynaklari",className:"text-gray-300 hover:text-white transition-colors",children:"İnsan Kaynakları"})}),(0,s.jsx)("li",{children:(0,s.jsx)(l(),{href:"/hizmetlerimiz/kurumsal-danismanlik",className:"text-gray-300 hover:text-white transition-colors",children:"Kurumsal Danışmanlık"})})]})]})]}),(0,s.jsx)("div",{className:"border-t border-gray-800 mt-12 pt-8",children:(0,s.jsxs)("div",{className:"flex flex-col md:flex-row justify-between items-center",children:[(0,s.jsx)("p",{className:"text-gray-400 text-xs md:text-sm text-center md:text-left",children:"\xa9 2025 Meta Analiz M\xfcşavirlik. T\xfcm hakları saklıdır."}),(0,s.jsxs)("div",{className:"flex space-x-6 mt-4 md:mt-0",children:[(0,s.jsx)(l(),{href:"/gizlilik-politikasi",className:"text-gray-400 hover:text-white text-sm transition-colors",children:"Gizlilik Politikası"}),(0,s.jsx)(l(),{href:"/kullanim-kosullari",className:"text-gray-400 hover:text-white text-sm transition-colors",children:"Kullanım Koşulları"})]})]})})]})})}},9576:(e,a,r)=>{"use strict";r.r(a),r.d(a,{GlobalError:()=>i.a,__next_app__:()=>c,pages:()=>d,routeModule:()=>x,tree:()=>m});var s=r(5239),t=r(8088),l=r(8170),i=r.n(l),n=r(893),o={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>n[e]);r.d(a,o);let m={children:["",{children:["toplanti-planla",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,8298)),"C:\\Users\\<USER>\\Desktop\\Meta A\\metaanalizmusavirlik\\Meta Analiz Musavirlik\\app\\toplanti-planla\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,8014)),"C:\\Users\\<USER>\\Desktop\\Meta A\\metaanalizmusavirlik\\Meta Analiz Musavirlik\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,5284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,d=["C:\\Users\\<USER>\\Desktop\\Meta A\\metaanalizmusavirlik\\Meta Analiz Musavirlik\\app\\toplanti-planla\\page.tsx"],c={require:r,loadChunk:()=>Promise.resolve()},x=new s.AppPageRouteModule({definition:{kind:t.RouteKind.APP_PAGE,page:"/toplanti-planla/page",pathname:"/toplanti-planla",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:m}})}};var a=require("../../webpack-runtime.js");a.C(e);var r=e=>a(a.s=e),s=a.X(0,[447,357,814,474,543],()=>r(9576));module.exports=s})();