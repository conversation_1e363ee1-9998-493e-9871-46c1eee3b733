(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[789],{2116:(e,l,r)=>{"use strict";r.r(l),r.d(l,{default:()=>d});var i=r(5155),a=r(7864),s=r(5922),t=r(6874),n=r.n(t);function d(){return(0,i.jsxs)("main",{className:"min-h-screen",children:[(0,i.jsx)(a.default,{currentPage:"hizmetlerimiz"}),(0,i.jsx)("section",{className:"pt-32 pb-16 bg-gradient-to-br from-slate-900 via-slate-800 to-slate-700",children:(0,i.jsx)("div",{className:"container mx-auto px-6",children:(0,i.jsxs)("div",{className:"text-center text-white",children:[(0,i.jsxs)("div",{className:"inline-flex items-center px-4 py-2 bg-white/10 rounded-full text-white text-sm font-medium mb-6 border border-white/20",children:[(0,i.jsx)("span",{className:"w-2 h-2 bg-blue-500 rounded-full mr-3"}),"Hukuki Uzman"]}),(0,i.jsx)("h1",{className:"text-4xl md:text-6xl font-light mb-6 text-white",children:"SGK Uyuşmazlıkları"}),(0,i.jsx)("p",{className:"text-xl text-white/90 max-w-3xl mx-auto",children:"Sosyal G\xfcvenlik Kurumu ile yaşanan uyuşmazlıklarda hukuki destek ve danışmanlık"})]})})}),(0,i.jsx)("section",{className:"py-24 bg-white",children:(0,i.jsx)("div",{className:"container mx-auto px-6",children:(0,i.jsx)("div",{className:"max-w-6xl mx-auto",children:(0,i.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8",children:[(0,i.jsxs)("div",{className:"bg-gradient-to-br from-red-50 to-red-100 p-8 rounded-2xl",children:[(0,i.jsx)("div",{className:"w-16 h-16 bg-red-600 rounded-xl flex items-center justify-center mb-6",children:(0,i.jsx)("svg",{className:"w-8 h-8 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,i.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"})})}),(0,i.jsx)("h3",{className:"text-2xl font-semibold mb-4 text-gray-900",children:"Prim Uyuşmazlıkları"}),(0,i.jsx)("p",{className:"text-gray-700 leading-relaxed mb-4",children:"Prim borcu, prim matrahı ve \xf6deme uyuşmazlıklarında hukuki destek"}),(0,i.jsxs)("ul",{className:"text-sm text-gray-600 space-y-2",children:[(0,i.jsx)("li",{children:"• Prim borcu itirazları"}),(0,i.jsx)("li",{children:"• Matrah uyuşmazlıkları"}),(0,i.jsx)("li",{children:"• Gecikme faizi itirazları"}),(0,i.jsx)("li",{children:"• \xd6deme planı m\xfczakereleri"})]})]}),(0,i.jsxs)("div",{className:"bg-gradient-to-br from-blue-50 to-blue-100 p-8 rounded-2xl",children:[(0,i.jsx)("div",{className:"w-16 h-16 bg-blue-600 rounded-xl flex items-center justify-center mb-6",children:(0,i.jsx)("svg",{className:"w-8 h-8 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,i.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"})})}),(0,i.jsx)("h3",{className:"text-2xl font-semibold mb-4 text-gray-900",children:"İş Kazası Uyuşmazlıkları"}),(0,i.jsx)("p",{className:"text-gray-700 leading-relaxed mb-4",children:"İş kazası tespiti, tazminat ve tedavi giderleri uyuşmazlıkları"}),(0,i.jsxs)("ul",{className:"text-sm text-gray-600 space-y-2",children:[(0,i.jsx)("li",{children:"• İş kazası tespit itirazları"}),(0,i.jsx)("li",{children:"• Tedavi gideri talepleri"}),(0,i.jsx)("li",{children:"• Ge\xe7ici iş g\xf6remezlik"}),(0,i.jsx)("li",{children:"• S\xfcrekli iş g\xf6remezlik"})]})]}),(0,i.jsxs)("div",{className:"bg-gradient-to-br from-green-50 to-green-100 p-8 rounded-2xl",children:[(0,i.jsx)("div",{className:"w-16 h-16 bg-green-600 rounded-xl flex items-center justify-center mb-6",children:(0,i.jsx)("svg",{className:"w-8 h-8 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,i.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"})})}),(0,i.jsx)("h3",{className:"text-2xl font-semibold mb-4 text-gray-900",children:"Emeklilik Uyuşmazlıkları"}),(0,i.jsx)("p",{className:"text-gray-700 leading-relaxed mb-4",children:"Emeklilik başvuru reddi, maaş hesaplama ve hak kayıpları"}),(0,i.jsxs)("ul",{className:"text-sm text-gray-600 space-y-2",children:[(0,i.jsx)("li",{children:"• Emeklilik red itirazları"}),(0,i.jsx)("li",{children:"• Maaş hesaplama hataları"}),(0,i.jsx)("li",{children:"• Hizmet s\xfcresi uyuşmazlıkları"}),(0,i.jsx)("li",{children:"• Bor\xe7lanma itirazları"})]})]}),(0,i.jsxs)("div",{className:"bg-gradient-to-br from-purple-50 to-purple-100 p-8 rounded-2xl",children:[(0,i.jsx)("div",{className:"w-16 h-16 bg-purple-600 rounded-xl flex items-center justify-center mb-6",children:(0,i.jsx)("svg",{className:"w-8 h-8 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,i.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"})})}),(0,i.jsx)("h3",{className:"text-2xl font-semibold mb-4 text-gray-900",children:"Denetim Uyuşmazlıkları"}),(0,i.jsx)("p",{className:"text-gray-700 leading-relaxed mb-4",children:"SGK denetim raporları ve tespit edilen eksikliklere itiraz"}),(0,i.jsxs)("ul",{className:"text-sm text-gray-600 space-y-2",children:[(0,i.jsx)("li",{children:"• Denetim raporu itirazları"}),(0,i.jsx)("li",{children:"• Eksik bildirge tespitleri"}),(0,i.jsx)("li",{children:"• Ceza itirazları"}),(0,i.jsx)("li",{children:"• Uzlaşma m\xfczakereleri"})]})]}),(0,i.jsxs)("div",{className:"bg-gradient-to-br from-orange-50 to-orange-100 p-8 rounded-2xl",children:[(0,i.jsx)("div",{className:"w-16 h-16 bg-orange-600 rounded-xl flex items-center justify-center mb-6",children:(0,i.jsx)("svg",{className:"w-8 h-8 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,i.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"})})}),(0,i.jsx)("h3",{className:"text-2xl font-semibold mb-4 text-gray-900",children:"Sağlık Uyuşmazlıkları"}),(0,i.jsx)("p",{className:"text-gray-700 leading-relaxed mb-4",children:"Sağlık hizmetleri, rapor ve tedavi giderleri uyuşmazlıkları"}),(0,i.jsxs)("ul",{className:"text-sm text-gray-600 space-y-2",children:[(0,i.jsx)("li",{children:"• Sağlık raporu itirazları"}),(0,i.jsx)("li",{children:"• Tedavi gideri talepleri"}),(0,i.jsx)("li",{children:"• Protez ve ortez talepleri"}),(0,i.jsx)("li",{children:"• Sağlık kurulu kararları"})]})]}),(0,i.jsxs)("div",{className:"bg-gradient-to-br from-indigo-50 to-indigo-100 p-8 rounded-2xl",children:[(0,i.jsx)("div",{className:"w-16 h-16 bg-indigo-600 rounded-xl flex items-center justify-center mb-6",children:(0,i.jsx)("svg",{className:"w-8 h-8 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,i.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M3 6l3 1m0 0l-3 9a5.002 5.002 0 006.001 0M6 7l3 9M6 7l6-2m6 2l3-1m-3 1l-3 9a5.002 5.002 0 006.001 0M18 7l3 9m-3-9l-6-2m0-2v2m0 16V5m0 16l3-1m-3 1l-3-1"})})}),(0,i.jsx)("h3",{className:"text-2xl font-semibold mb-4 text-gray-900",children:"Hukuki S\xfcre\xe7 Y\xf6netimi"}),(0,i.jsx)("p",{className:"text-gray-700 leading-relaxed mb-4",children:"İdari ve adli yargı s\xfcre\xe7lerinde profesyonel temsil"}),(0,i.jsxs)("ul",{className:"text-sm text-gray-600 space-y-2",children:[(0,i.jsx)("li",{children:"• İdari yargı s\xfcre\xe7leri"}),(0,i.jsx)("li",{children:"• Adli yargı takibi"}),(0,i.jsx)("li",{children:"• Hukuki temsil"}),(0,i.jsx)("li",{children:"• Dosya hazırlığı"})]})]})]})})})}),(0,i.jsx)("section",{className:"py-16 bg-gradient-to-br from-slate-900 via-slate-800 to-slate-700",children:(0,i.jsx)("div",{className:"container mx-auto px-6",children:(0,i.jsxs)("div",{className:"text-center text-white",children:[(0,i.jsx)("h3",{className:"text-3xl md:text-4xl font-light mb-6",children:"SGK Haklarınızı Koruyun"}),(0,i.jsx)("p",{className:"text-xl text-white/90 mb-8 max-w-2xl mx-auto",children:"Uzman hukuk ekibimizle SGK uyuşmazlıklarınızı \xe7\xf6z\xfcme kavuşturun"}),(0,i.jsxs)(n(),{href:"/iletisim",className:"inline-flex items-center px-8 py-4 bg-white text-slate-900 font-semibold rounded-lg hover:bg-gray-100 transition-colors text-lg",children:["İletişime Ge\xe7in",(0,i.jsx)("svg",{className:"w-5 h-5 ml-2",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,i.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M17 8l4 4m0 0l-4 4m4-4H3"})})]})]})})}),(0,i.jsx)(s.A,{})]})}},6708:(e,l,r)=>{Promise.resolve().then(r.bind(r,2116))}},e=>{var l=l=>e(e.s=l);e.O(0,[874,766,49,441,684,358],()=>l(6708)),_N_E=e.O()}]);